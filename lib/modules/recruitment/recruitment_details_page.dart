import 'dart:convert';
import 'dart:typed_data';

import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:superapps_cms/components/chip.dart';
import 'package:superapps_cms/modules/recruitment/recruitment_details_controller.dart';
import 'package:superapps_cms/modules/recruitment/recruitment_details_page_tabs.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/extension.dart';
import 'package:superapps_cms/utils/utils.dart';
import 'package:superapps_cms/utils/validation_constants.dart';
import 'package:toastification/toastification.dart';

class RecruitmentDetails extends StatelessWidget {
  RecruitmentDetails({super.key, required this.id})
      : controller = Get.put(
          RecruitmentDetailsController(id),
          tag: getRandomString(),
        );

  final String id;
  final RecruitmentDetailsController controller;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kembali').medium(weight: FontWeight.bold),
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => controller.goBack(),
        ),
      ),
      body: Container(
        margin: const EdgeInsets.fromLTRB(paddingMedium, 0, paddingMedium, paddingMedium),
        child: Card(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Retry submit to compass button (conditional)
                  Obx(() {
                    if (controller.profileController.isLoading.isTrue) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }
                    if (controller.shouldShowRetryButton.value) {
                      return Column(
                        children: [
                          Obx(() => SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed:
                                      controller.isRetryLoading.value ? null : () => controller.retrySubmitToCompass(),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(vertical: 15),
                                  ),
                                  child: controller.isRetryLoading.value
                                      ? const SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: Colors.white,
                                          ),
                                        )
                                      : const Text('Retry submit to compass'),
                                ),
                              )),
                          const SizedBox(height: paddingLarge),
                        ],
                      );
                    }
                    return const SizedBox.shrink();
                  }),

                  // Informasi Perekrut Section
                  _buildSection(
                    title: 'Informasi Perekrut',
                    content: _buildPerekrutInfoContent(),
                  ),

                  const SizedBox(height: paddingLarge),

                  // Informasi Kandidat Section
                  _buildSection(
                    title: 'Informasi Kandidat',
                    content: _buildKandidatInfoContent(),
                  ),

                  const SizedBox(height: paddingLarge),

                  // Result and Status Persetujuan Section (side by side on desktop, stacked on mobile)
                  _buildResultAndStatusSection(),

                  const SizedBox(height: paddingLarge),

                  // Dokumen Section
                  _buildDocumentSection(),

                  const SizedBox(height: paddingLarge),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSection({required String title, required Widget content, bool includeDivider = true}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title).displayMedium(weight: FontWeight.bold, color: Colors.black),
        const SizedBox(height: paddingMedium),
        content,
        if (includeDivider) const Divider(height: paddingLarge, thickness: 1),
      ],
    );
  }

  Widget _buildPerekrutInfoContent() {
    return Obx(() {
      if (controller.approvalData.value == null) {
        return const Center(child: CircularProgressIndicator());
      }

      final data = controller.approvalData.value!;
      final requestBy = data.requestBy;
      final branch = requestBy.branches.isNotEmpty ? requestBy.branches[0] : null;
      final branchName = branch != null ? "${branch.branchCode} - ${branch.branchName}" : "N/A";
      final createdAt = data.createdAt.formatToString(format: dateHumanLong);

      // Get chip type based on status
      ChipType statusChipType;
      switch (data.approvalStatus) {
        case STATUS_DISETUJUI:
          statusChipType = ChipType.success;
          break;
        case STATUS_DITOLAK:
          statusChipType = ChipType.danger;
          break;
        case STATUS_TERTUNDA:
          statusChipType = ChipType.warning;
          break;
        case STATUS_MENUNGGU_PERSETUJUAN:
          statusChipType = ChipType.info;
          break;
        case STATUS_DIBATALKAN:
          statusChipType = ChipType.danger;
          break;
        default:
          statusChipType = ChipType.info;
      }

      return LayoutBuilder(builder: (context, constraints) {
        // Use column layout for mobile (width < 768)
        if (constraints.maxWidth < 768) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InfoRow('Kode Agen', requestBy.agentCode),
              InfoRow('Level', requestBy.agentLevel),
              InfoRow('Nama Agen', requestBy.name),
              InfoRow('Kode Cabang', branchName),
              InfoRow('Tanggal Pengajuan', createdAt),
              const SizedBox(height: paddingExtraSmall),
              _buildStatusRow('Status', data.approvalStatus, statusChipType),
            ],
          );
        } else {
          // Desktop layout (width >= 768)
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InfoRow('Kode Agen', requestBy.agentCode),
                    InfoRow('Level', requestBy.agentLevel),
                    InfoRow('Nama Agen', requestBy.name),
                  ],
                ),
              ),

              // Right column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InfoRow('Kode Cabang', branchName),
                    InfoRow('Tanggal Pengajuan', createdAt),
                    _buildStatusRow('Status', data.approvalStatus, statusChipType),
                  ],
                ),
              ),
            ],
          );
        }
      });
    });
  }

  Widget _buildDocumentSection() {
    return Obx(() {
      if (controller.approvalData.value == null) {
        return const Center(child: CircularProgressIndicator());
      }

      final data = controller.approvalData.value!;
      final detailData = data.detailData;

      // Get document URLs from detailData
      final pkajFile = detailData['pkajFile'];
      final pmkajFile = detailData['pmkajFile'];
      final apgenFile = detailData['apgenFile'];
      final kodeEtikFile = detailData['kodeEtikFile'];
      final antiTwistingFile = detailData['antiTwistingFile'];

      // Create list of available documents
      final List<Map<String, String>> documents = [];

      if (pkajFile != null && pkajFile.toString().isNotEmpty) {
        documents.add({
          'title': 'PKAJ',
          'url': pkajFile.toString(),
          'number': detailData['pkajNumber']?.toString() ?? '',
        });
      }

      if (pmkajFile != null && pmkajFile.toString().isNotEmpty) {
        documents.add({
          'title': 'PMKAJ',
          'url': pmkajFile.toString(),
          'number': detailData['pmkajNumber']?.toString() ?? '',
        });
      }

      if (apgenFile != null && apgenFile.toString().isNotEmpty) {
        documents.add({
          'title': 'APGEN',
          'url': apgenFile.toString(),
          'number': '',
        });
      }

      if (kodeEtikFile != null && kodeEtikFile.toString().isNotEmpty) {
        documents.add({
          'title': 'Kode Etik',
          'url': kodeEtikFile.toString(),
          'number': '',
        });
      }

      if (antiTwistingFile != null && antiTwistingFile.toString().isNotEmpty) {
        documents.add({
          'title': 'Anti Twisting',
          'url': antiTwistingFile.toString(),
          'number': '',
        });
      }

      // If no documents available, show empty state
      if (documents.isEmpty) {
        return Container();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          const SizedBox(height: paddingMedium),

          const Text('Dokumen').displayMedium(weight: FontWeight.bold, color: Colors.black),
          const SizedBox(height: paddingMedium),

          // Document information section
          _buildDocumentInfoSection(detailData),

          const SizedBox(height: paddingLarge),

          // Document files section
          LayoutBuilder(builder: (context, constraints) {
            // Use column layout for mobile (width < 768)
            if (constraints.maxWidth < 768) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children:
                    documents.map((doc) => _buildDocumentCard(doc['title']!, doc['url']!, doc['number']!)).toList(),
              );
            } else {
              // Desktop layout (width >= 768) - Two columns
              return Wrap(
                spacing: paddingMedium,
                runSpacing: paddingMedium,
                children: documents
                    .map((doc) => SizedBox(
                          width: (constraints.maxWidth - paddingMedium) / 2,
                          child: _buildDocumentCard(doc['title']!, doc['url']!, doc['number']!),
                        ))
                    .toList(),
              );
            }
          }),
        ],
      );
    });
  }

  Widget _buildDocumentInfoSection(Map<String, dynamic> detailData) {
    // Get document information
    final pkajNumber = detailData['pkajNumber']?.toString();
    final pmkajNumber = detailData['pmkajNumber']?.toString();
    final effectiveDate = detailData['effectiveDate']?.toString();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (pkajNumber != null && pkajNumber.isNotEmpty) InfoRow('Nomor PKAJ', pkajNumber),
        if (pmkajNumber != null && pmkajNumber.isNotEmpty) InfoRow('Nomor PMKAJ', pmkajNumber),
        if (effectiveDate != null && effectiveDate.isNotEmpty)
          InfoRow('Tanggal Efektif', effectiveDate.toFormattedDate(outputFormat: dateHumanLong) ?? effectiveDate),
      ],
    );
  }

  Widget _buildKandidatInfoContent() {
    return Obx(() {
      if (controller.approvalData.value == null) {
        return const Center(child: CircularProgressIndicator());
      }

      final data = controller.approvalData.value!;
      final detailData = data.detailData;

      // Get agent data from the new structure
      final agentData = detailData;

      // Get branch info from agent's branches array
      // final branches = agentData['branches'] as List<dynamic>?;
      // final branch = branches != null && branches.isNotEmpty
      //     ? "${branches[0]['branchCode']} - ${branches[0]['branchName']}"
      //     : "N/A";
      final branch = agentData['branch']['branchName'];

      // Get bank info from agent data
      final bank = agentData['bank'];

      return LayoutBuilder(builder: (context, constraints) {
        // Use column layout for mobile (width < 768)
        if (constraints.maxWidth < 768) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Photo section - centered on mobile
              Center(
                child: Column(
                  children: [
                    Container(
                      height: 200,
                      width: 200,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(radius),
                      ),
                      child: Stack(
                        children: [
                          Center(
                            child: ClipRRect(
                              borderRadius: const BorderRadius.all(Radius.circular(radius)),
                              child: _buildProfileImage(
                                detailData['passPhoto'],
                                height: 200,
                                width: 200,
                              ),
                            ),
                          ),
                          Positioned(
                            right: 5,
                            bottom: 5,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: IconButton(
                                icon: const Icon(Icons.zoom_in, color: Colors.blue),
                                onPressed: () {},
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: paddingMedium),

              // Personal info
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InfoRow('Nama Lengkap', agentData['fullName'] ?? 'N/A'),
                  InfoRow('Level', agentData['positionLevel'] ?? 'N/A'),
                  // InfoRow('Kode Agen', agentData['agentCode'] ?? 'N/A'),
                  InfoRow('Kantor Cabang', branch),
                  InfoRow('Nomor KTP', agentData['nik']), // KTP number not available in new structure
                  InfoRow('Nomor HP', agentData['phoneNumber'] ?? 'N/A'),
                  InfoRow('Email', agentData['email'] ?? 'N/A'),
                  InfoRow('Nama Pemilik Rekening', agentData['bankAccountName']), // Bank account name not available
                  InfoRow('Nama Bank', bank != null ? bank['bankName'] : 'N/A'),
                  InfoRow('Nomor Rekening', agentData['bankAccountNumber'] ?? 'N/A'),
                ],
              ),

              const SizedBox(height: paddingMedium),

              // Document uploads
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // KTP Document
                  const Text('Foto KTP').medium(weight: FontWeight.bold),
                  const SizedBox(height: paddingSmall),
                  _buildDocumentImage('KTP', detailData['ktpPhoto']),

                  const SizedBox(height: paddingMedium),

                  const Text('Selfie Foto').medium(weight: FontWeight.bold),
                  const SizedBox(height: paddingSmall),
                  _buildDocumentImage('Selfi', detailData['selfiePhoto']),
                ],
              ),

              const SizedBox(height: paddingLarge),

              // Check if position level is BP - hide tabs if true
              if (agentData['positionLevel'] != 'BP') ...[
                // Tab-like navigation
                buildTabNavigation(controller),

                const SizedBox(height: paddingMedium),

                // Tab content
                buildTabContent(controller),
              ],
            ],
          );
        } else {
          // Desktop layout (width >= 768)
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Photo section - left
                  SizedBox(
                    width: 250,
                    child: Column(
                      children: [
                        Container(
                          height: 230,
                          width: 230,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(radius),
                          ),
                          child: Stack(
                            children: [
                              Center(
                                child: ClipRRect(
                                  borderRadius: const BorderRadius.all(Radius.circular(radius)),
                                  child: _buildProfileImage(
                                    detailData['passPhoto'],
                                    height: 230,
                                    width: 230,
                                  ),
                                ),
                              ),
                              Positioned(
                                right: 5,
                                bottom: 5,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                    border: Border.all(color: Colors.grey.shade300),
                                  ),
                                  child: IconButton(
                                    icon: const Icon(Icons.zoom_in, color: Colors.blue),
                                    onPressed: () => showImagePopup(detailData['passPhoto'], title: 'Foto Profil'),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Personal info - middle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InfoRow('Nama Lengkap', agentData['fullName'] ?? 'N/A'),
                        InfoRow('Level', agentData['positionLevel'] ?? 'N/A'),
                        // InfoRow('Kode Agen', agentData['agentCode'] ?? 'N/A'),
                        InfoRow('Kantor Cabang', branch),
                        InfoRow('Nomor KTP', agentData['nik']), // KTP number not available in new structure
                        InfoRow('Nomor HP', agentData['phoneNumber'] ?? 'N/A'),
                        InfoRow('Email', agentData['email'] ?? 'N/A'),
                        InfoRow(
                            'Nama Pemilik Rekening', agentData['bankAccountName']), // Bank account name not available
                        InfoRow('Nama Bank', bank != null ? bank['bankName'] : 'N/A'),
                        InfoRow('Nomor Rekening', agentData['bankAccountNumber'] ?? 'N/A'),
                      ],
                    ),
                  ),

                  // Document uploads - right
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // KTP Document
                        const Text('Foto KTP').medium(weight: FontWeight.bold),
                        const SizedBox(height: paddingSmall),
                        _buildDocumentImage('KTP', detailData['ktpPhoto']),

                        const SizedBox(height: paddingMedium),

                        const Text('Selfie Foto').medium(weight: FontWeight.bold),
                        const SizedBox(height: paddingSmall),
                        _buildDocumentImage('Selfi', detailData['selfiePhoto']),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: paddingLarge),

              // Check if position level is BP - hide tabs if true
              if (agentData['positionLevel'] != 'BP') ...[
                // Tab-like navigation
                buildTabNavigation(controller),

                const SizedBox(height: paddingMedium),

                // Tab content
                buildTabContent(controller),
              ],
            ],
          );
        }
      });
    });
  }

  Widget _buildDocumentCard(String title, String url, String number) {
    return InkWell(
      onTap: () => _openDocument(url),
      child: Card(
        margin: const EdgeInsets.only(bottom: paddingSmall),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDocumentListTile(title, url, number),
            _buildDocumentPreviewSection(title, url),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentListTile(String title, String url, String number) {
    return ListTile(
      leading: _getDocumentIcon(title),
      title: Text(title).medium(weight: FontWeight.bold),
      onTap: () => _openDocument(url),
    );
  }

  Widget _buildDocumentPreviewSection(String title, String url) {
    // For PDF documents, show a simple preview placeholder
    return Container(
      padding: const EdgeInsets.all(paddingMedium),
      margin: const EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(radius),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.picture_as_pdf, color: Colors.red.shade600, size: 24),
                const SizedBox(height: 4),
                Text('PDF',
                    style: TextStyle(
                      color: Colors.red.shade600,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    )),
              ],
            ),
          ),
          const SizedBox(width: paddingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(_getFileNameFromUrl(url)).medium(),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.download, color: Colors.blue),
            onPressed: () => _openDocument(url),
          ),
        ],
      ),
    );
  }

  Widget _getDocumentIcon(String documentType) {
    switch (documentType.toUpperCase()) {
      case 'PKAJ':
      case 'PMKAJ':
      case 'APGEN':
      case 'KODE ETIK':
      case 'ANTI TWISTING':
        return const Icon(Icons.description, color: Colors.blue);
      default:
        return const Icon(Icons.attach_file, color: Colors.blue);
    }
  }

  String _getFileNameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final segments = uri.pathSegments;
      if (segments.isNotEmpty) {
        return segments.last;
      }
      return url.split('/').last;
    } catch (e) {
      return url.split('/').last;
    }
  }

  Future<void> _openDocument(String url) async {
    try {
      logger.d('Attempting to download document: $url');

      // Download file using file_saver
      await _downloadFileWithSaver(url);
    } catch (e) {
      logger.e('Error downloading document: $e');
      toast(text: "Error mendownload dokumen: $e", type: ToastificationType.error);
    }
  }

  Future<void> _downloadFileWithSaver(String url) async {
    try {
      // Fetch the file from URL
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        // Get file bytes
        final Uint8List bytes = response.bodyBytes;

        // Extract filename from URL or use default
        String fileName = _getFileNameFromUrl(url);
        if (fileName.isEmpty || !fileName.contains('.')) {
          fileName = 'document.pdf'; // Default filename
        }

        // Save file using file_saver
        final result = await FileSaver.instance.saveFile(
          name: fileName, // full filename with extension
          bytes: bytes,
          mimeType: _getMimeType(fileName),
        );

        toast(text: "File berhasil didownload: $fileName", type: ToastificationType.success);
        logger.d('File saved successfully: $result');
      } else {
        throw Exception('Failed to fetch file: ${response.statusCode}');
      }
    } catch (e) {
      logger.e('Error in _downloadFileWithSaver: $e');
      toast(text: "Error mendownload file: $e", type: ToastificationType.error);
    }
  }

  MimeType _getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return MimeType.pdf;
      case 'doc':
        return MimeType.microsoftWord;
      case 'docx':
        return MimeType.microsoftWord;
      case 'jpg':
      case 'jpeg':
        return MimeType.jpeg;
      case 'png':
        return MimeType.png;
      case 'txt':
        return MimeType.text;
      default:
        return MimeType.other;
    }
  }

  Widget _buildDocumentImage(String label, String? imageSource) {
    if (imageSource == null || imageSource.isEmpty) {
      return _buildDocumentPreview('$label tidak tersedia', '0 KB');
    }

    // Check if the string is a URL
    bool isUrl = imageSource.startsWith('http://') || imageSource.startsWith('https://');

    if (isUrl) {
      // Handle URL image
      return InkWell(
        onTap: () => showImagePopup(imageSource, title: 'Foto $label'),
        child: Container(
          padding: const EdgeInsets.all(paddingSmall),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(radius),
          ),
          child: Row(
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(radius),
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(radius),
                      child: Image.network(
                        imageSource,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => const Icon(
                          Icons.insert_photo,
                          size: 30,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                    Positioned(
                      right: 5,
                      bottom: 5,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(128),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Icon(
                          Icons.zoom_in,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: paddingSmall),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Foto $label').medium(),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // Handle base64 image
      try {
        final decodedBytes = base64Decode(imageSource);
        final size = decodedBytes.length / 1024; // Size in KB
        final sizeText = size > 1024 ? '${(size / 1024).toStringAsFixed(1)} MB' : '${size.toStringAsFixed(1)} KB';

        return InkWell(
          onTap: () => showImagePopup(imageSource, title: 'Foto $label'),
          child: Container(
            padding: const EdgeInsets.all(paddingSmall),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(radius),
            ),
            child: Row(
              children: [
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(radius),
                  ),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(radius),
                        child: Image.memory(
                          decodedBytes,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => const Icon(
                            Icons.insert_photo,
                            size: 30,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      Positioned(
                        right: 5,
                        bottom: 5,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.black.withAlpha(128),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Icon(
                            Icons.zoom_in,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: paddingSmall),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Foto $label').medium(),
                      Text(sizeText).small(color: Colors.grey),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      } catch (e) {
        logger.e('Error decoding image: $e');
        return _buildDocumentPreview('Error loading $label', '0 KB');
      }
    }
  }

  Widget _buildProfileImage(String? imageSource, {required double height, required double width}) {
    if (imageSource == null || imageSource.isEmpty) {
      return Placeholder(
        fallbackHeight: height,
        fallbackWidth: width,
        color: Colors.grey,
      );
    }

    // Check if the string is a URL
    bool isUrl = imageSource.startsWith('http://') || imageSource.startsWith('https://');

    if (isUrl) {
      // Handle URL image
      return InkWell(
        onTap: () => showImagePopup(imageSource, title: 'Foto Profil'),
        child: Image.network(
          imageSource,
          height: height,
          width: width,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => Placeholder(
            fallbackHeight: height,
            fallbackWidth: width,
            color: Colors.grey,
          ),
        ),
      );
    } else {
      // Handle base64 image
      try {
        final decodedBytes = base64Decode(imageSource);
        return InkWell(
          onTap: () => showImagePopup(imageSource, title: 'Foto Profil'),
          child: Image.memory(
            decodedBytes,
            height: height,
            width: width,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Placeholder(
              fallbackHeight: height,
              fallbackWidth: width,
              color: Colors.grey,
            ),
          ),
        );
      } catch (e) {
        logger.e('Error decoding profile image: $e');
        return Placeholder(
          fallbackHeight: height,
          fallbackWidth: width,
          color: Colors.grey,
        );
      }
    }
  }

  Widget _buildDocumentPreview(String filename, String filesize) {
    return Container(
      padding: const EdgeInsets.all(paddingSmall),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(radius),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(radius),
              color: Colors.grey.shade200,
            ),
            child: const Center(
              child: Icon(Icons.insert_photo, size: 30, color: Colors.grey),
            ),
          ),
          const SizedBox(width: paddingSmall),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(filename).medium(),
                Text(filesize).small(color: Colors.grey),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultContent() {
    return Obx(() {
      if (controller.approvalData.value == null) {
        return const Center(child: CircularProgressIndicator());
      }

      // Use the validation status fields from the controller
      final ktpStatus = controller.ktpStatus.value;
      final bankStatus = controller.bankAccountStatus.value;
      final hirarkiStatus = controller.hirarkiStatus.value;
      final adminStatus = controller.administrationAgentStatus.value;
      final aajiStatus = controller.licenseAajiStatus.value;
      final aasiStatus = controller.licenseAasiStatus.value;
      final amlStatus = controller.amlStatus.value;
      final blacklistStatus = controller.blacklistStatus.value;

      // Check if user can edit - only reviewers can edit validation fields
      final canEdit = controller.isCurrentUserReviewer.value && !controller.actionsDisabled.value;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Flat layout with labels on the left and dropdowns on the right
          ValidationRow(
            label: 'KTP',
            validationType: 'validationKtpStatus',
            value: ktpStatus,
            isDisabled: !canEdit,
            onChanged: controller.updateKtpStatus,
          ),
          ValidationRow(
            label: 'Hirarki',
            validationType: 'validationHirarkiStatus',
            value: hirarkiStatus,
            isDisabled: !canEdit,
            onChanged: controller.updateHirarkiStatus,
          ),
          ValidationRow(
            label: 'Blacklist / Watchlist',
            validationType: 'validationBlacklistStatus',
            value: blacklistStatus,
            isDisabled: !canEdit,
            onChanged: controller.updateBlacklistStatus,
          ),
          ValidationRow(
            label: 'Lisensi AAJI',
            validationType: 'validationLicenseAajiStatus',
            value: aajiStatus,
            isDisabled: !canEdit,
            onChanged: controller.updateLicenseAajiStatus,
          ),
          ValidationRow(
            label: 'Lisensi AASI',
            validationType: 'validationLicenseAasiStatus',
            value: aasiStatus,
            isDisabled: !canEdit,
            onChanged: controller.updateLicenseAasiStatus,
          ),
          ValidationRow(
            label: 'Nomor Rekening',
            validationType: 'validationBankAccountStatus',
            value: bankStatus,
            isDisabled: !canEdit,
            onChanged: controller.updateBankAccountStatus,
          ),
          ValidationRow(
            label: 'AML',
            validationType: 'validationAmlStatus',
            value: amlStatus,
            isDisabled: !canEdit,
            onChanged: controller.updateAmlStatus,
          ),
          ValidationRow(
            label: 'Administrasi Agen',
            validationType: 'validationAdministrationAgentStatus',
            value: adminStatus,
            isDisabled: !canEdit,
            onChanged: controller.updateAdministrationAgentStatus,
          ),
        ],
      );
    });
  }

  // Build hierarchy information section
  Widget _buildHierarchyInfoSection() {
    return Obx(() {
      if (controller.approvalData.value == null) {
        return const Center(child: CircularProgressIndicator());
      }

      final data = controller.approvalData.value!;
      final detailData = data.detailData;

      // Get agent data from the new structure
      final agentData = jsonDecode(detailData['resultValidationHirarki'] ?? '{}');

      // Extract hierarchy values directly from agent data
      final directLeaderName = agentData['directLeaderName'] ?? '';
      final directLeaderCode = agentData['directLeaderCode'] ?? '';
      var indirectLeaderName = agentData['indirectLeaderName'] ?? '';
      var indirectLeaderCode = agentData['indirectLeaderCode'] ?? '';

      return LayoutBuilder(builder: (context, constraints) {
        // Use column layout for mobile (width < 768)
        if (constraints.maxWidth < 768) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Direct leader info
              InfoRow('Direct Leader', directLeaderName),
              InfoRow('Kode Agent', directLeaderCode),

              if (indirectLeaderCode != '') const SizedBox(height: paddingMedium),

              // Indirect leader (BD) info
              if (indirectLeaderCode != '') InfoRow('Indirect Leader', indirectLeaderName),
              if (indirectLeaderCode != '') InfoRow('Kode Agent', indirectLeaderCode),
            ],
          );
        } else {
          // Desktop layout (width >= 768)
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left column - Direct leader
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InfoRow('Direct Leader', directLeaderName),
                    InfoRow('Kode Agent', directLeaderCode),
                  ],
                ),
              ),

              // Right column - Indirect leader (BD)
              if (indirectLeaderCode != '')
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      InfoRow('Indirect Leader', indirectLeaderName),
                      InfoRow('Kode Agent', indirectLeaderCode),
                    ],
                  ),
                ),
            ],
          );
        }
      });
    });
  }

  Widget _buildResultAndStatusSection() {
    return LayoutBuilder(builder: (context, constraints) {
      // Use column layout for mobile (width < 768)
      if (constraints.maxWidth < 768) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hierarchy Information Section
            _buildSection(
              title: 'Informasi Hirarki',
              content: _buildHierarchyInfoSection(),
            ),

            const SizedBox(height: paddingLarge),

            // Result Section
            _buildSection(
              title: 'Result',
              content: _buildResultContent(),
              includeDivider: false,
            ),

            const SizedBox(height: paddingLarge),

            // Status Approval section
            _buildSection(
              title: 'Status Approval',
              content: _buildStatusApprovalContent(),
              includeDivider: false,
            ),
          ],
        );
      } else {
        // Desktop layout (width >= 768) - Two columns
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hierarchy Information Section
            _buildSection(
              title: 'Informasi Hirarki',
              content: _buildHierarchyInfoSection(),
            ),

            const SizedBox(height: paddingLarge),

            // Result and Status in two columns
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left column - Result Section
                Expanded(
                  flex: 1,
                  child: _buildSection(
                    title: 'Result',
                    content: _buildResultContent(),
                    includeDivider: false,
                  ),
                ),

                const SizedBox(width: paddingLarge),

                // Right column - Status Persetujuan & Catatan
                Expanded(
                  flex: 1,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Status Approval section
                      _buildStatusApprovalContent(),
                    ],
                  ),
                ),
              ],
            ),
          ],
        );
      }
    });
  }

  Widget _buildStatusRow(String label, String value, ChipType type) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: paddingExtraSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(label).medium(),
          ),
          const Text(':').medium(),
          const SizedBox(width: paddingSmall),
          CustomChip(
            type: type,
            text: value,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusApprovalContent() {
    return Obx(() {
      if (controller.approvalData.value == null) {
        return const Center(child: CircularProgressIndicator());
      }

      final data = controller.approvalData.value!;

      // Check if user is allowed to approve
      final bool canApprove = controller.isCurrentUserApprover.value && !controller.actionsDisabled.value;

      return LayoutBuilder(builder: (context, constraints) {
        // Use column layout for mobile (width < 768)
        if (constraints.maxWidth < 768) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status approval section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Status Approval').medium(weight: FontWeight.bold),
                  const SizedBox(height: paddingSmall),
                  canApprove ? _buildStatusOptions() : _buildStatusDisplay(data.approvalStatus),
                ],
              ),

              const SizedBox(height: paddingMedium),

              // Approval history section
              // if (data.approvalDetails.isNotEmpty) ...[
              //   Column(
              //     crossAxisAlignment: CrossAxisAlignment.start,
              //     children: [
              //       const Text('Riwayat Approval').medium(weight: FontWeight.bold),
              //       const SizedBox(height: paddingSmall),
              //       _buildApprovalHistory(data.approvalDetails),
              //     ],
              //   ),
              //   const SizedBox(height: paddingMedium),
              // ],

              // Notes section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Catatan').medium(weight: FontWeight.bold),
                  const SizedBox(height: paddingSmall),
                  _buildNotesField(enabled: canApprove),
                ],
              ),

              const SizedBox(height: paddingMedium),

              // Submit button - only show if user can approve
              if (canApprove) _buildSubmitButton(),

              // Generate document button - only show if conditions are met
              Obx(() {
                if (controller.shouldShowGenerateDocumentButton.value) {
                  return Column(
                    children: [
                      const SizedBox(height: paddingMedium),
                      _buildGenerateDocumentButton(),
                    ],
                  );
                }
                return const SizedBox.shrink();
              }),
            ],
          );
        } else {
          // Desktop layout (width >= 768)
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status approval section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Status Approval').medium(weight: FontWeight.bold),
                  const SizedBox(height: paddingSmall),
                  canApprove ? _buildStatusOptions() : _buildStatusDisplay(data.approvalStatus),
                ],
              ),

              const SizedBox(height: paddingMedium),

              // Approval history section
              if (data.approvalDetails.isNotEmpty) ...[
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Riwayat Approval').medium(weight: FontWeight.bold),
                    const SizedBox(height: paddingSmall),
                    _buildApprovalHistory(data.approvalDetails),
                  ],
                ),
                const SizedBox(height: paddingMedium),
              ],

              // Notes section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Catatan').medium(weight: FontWeight.bold),
                  const SizedBox(height: paddingSmall),
                  _buildNotesField(enabled: canApprove),
                ],
              ),

              const SizedBox(height: paddingMedium),

              // Submit button - only show if user can approve
              if (canApprove) _buildSubmitButton(),

              // Generate document button - only show if conditions are met
              Obx(() {
                if (controller.shouldShowGenerateDocumentButton.value) {
                  return Column(
                    children: [
                      const SizedBox(height: paddingMedium),
                      _buildGenerateDocumentButton(),
                    ],
                  );
                }
                return const SizedBox.shrink();
              }),
            ],
          );
        }
      });
    });
  }

  Widget _buildStatusDisplay(String status) {
    ChipType chipType;
    switch (status) {
      case STATUS_DISETUJUI:
        chipType = ChipType.success;
        break;
      case STATUS_DITOLAK:
        chipType = ChipType.danger;
        break;
      case STATUS_TERTUNDA:
        chipType = ChipType.warning;
        break;
      case STATUS_MENUNGGU_PERSETUJUAN:
        chipType = ChipType.info;
        break;
      case STATUS_DIBATALKAN:
        chipType = ChipType.danger;
        break;
      default:
        chipType = ChipType.info;
    }

    return CustomChip(
      text: status,
      type: chipType,
    );
  }

  Widget _buildStatusOptions() {
    return Obx(() => Row(
          spacing: paddingSmall,
          children: [
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radius),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: RadioListTile<String>(
                  title: const Text('Proses').medium(),
                  value: "PROSES",
                  groupValue: controller.approvalStatus.value,
                  onChanged: (value) {
                    if (value != null) controller.approvalStatus.value = value;
                  },
                  contentPadding: const EdgeInsets.all(paddingSmall),
                  dense: true,
                ),
              ),
            ),
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radius),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: RadioListTile<String>(
                  title: const Text('Kembalikan').medium(),
                  value: "KEMBALIKAN",
                  groupValue: controller.approvalStatus.value,
                  onChanged: (value) {
                    if (value != null) controller.approvalStatus.value = value;
                  },
                  contentPadding: const EdgeInsets.all(paddingSmall),
                  dense: true,
                ),
              ),
            ),
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radius),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: RadioListTile<String>(
                  title: const Text('Tolak').medium(),
                  value: "TOLAK",
                  groupValue: controller.approvalStatus.value,
                  onChanged: (value) {
                    if (value != null) controller.approvalStatus.value = value;
                  },
                  contentPadding: const EdgeInsets.all(paddingSmall),
                  dense: true,
                ),
              ),
            ),
          ],
        ));
  }

  Widget _buildNotesField({bool enabled = true}) {
    return Container(
      height: 150,
      width: double.infinity,
      padding: const EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radius),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextField(
        controller: controller.notesController,
        enabled: enabled,
        decoration: const InputDecoration(
          border: InputBorder.none,
          hintText: 'Masukkan catatan di sini...',
          contentPadding: EdgeInsets.zero,
          fillColor: Colors.transparent,
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
        ),
        maxLines: 3,
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Obx(() => SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: (controller.isUploading.value || !controller.isFormValid.value)
                ? null
                : () => controller.submitApproval(),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 15),
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: controller.isUploading.value
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text('Simpan'),
          ),
        ));
  }

  Widget _buildGenerateDocumentButton() {
    return Obx(() => SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: controller.isGeneratingDocument.value ? null : () => controller.generateContractDocument(),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 15),
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: controller.isGeneratingDocument.value
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text('Generate Document'),
          ),
        ));
  }

  Widget _buildApprovalHistory(List<dynamic> approvalDetails) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(radius),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: approvalDetails.length,
        separatorBuilder: (context, index) => Divider(color: Colors.grey.shade300),
        itemBuilder: (context, index) {
          final detail = approvalDetails[index];

          // Handle both ApprovalDetailModel objects and Map objects
          String name = '';
          String agentCode = '';
          String remarks = 'Tidak ada catatan';
          String status = '';
          int level = 0;

          try {
            if (detail is Map) {
              // Handle Map format
              final actionBy = detail['actionBy'] as Map?;
              if (actionBy != null) {
                name = actionBy['name']?.toString() ?? '';
                agentCode = actionBy['agentCode']?.toString() ?? '';
              }
              remarks = detail['remarks']?.toString() ?? 'Tidak ada catatan';
              status = detail['approvalStatus']?.toString() ?? '';
              level = detail['levelNumber'] is int ? detail['levelNumber'] : 0;
            } else {
              // Handle ApprovalDetailModel format
              // Access properties directly using reflection
              final detailMap = detail.toJson();
              final actionBy = detailMap['actionBy'] as Map?;
              if (actionBy != null) {
                name = actionBy['name']?.toString() ?? '';
                agentCode = actionBy['agentCode']?.toString() ?? '';
              }
              remarks = detailMap['remarks']?.toString() ?? 'Tidak ada catatan';
              status = detailMap['approvalStatus']?.toString() ?? '';
              level = detailMap['levelNumber'] is int ? detailMap['levelNumber'] : 0;
            }
          } catch (e) {
            logger.e('Error parsing approval detail: $e');
          }

          return ListTile(
            title: Row(
              children: [
                Text('Level $level: $name ${agentCode.isNotEmpty ? "($agentCode)" : ""}').medium(),
                const SizedBox(width: paddingSmall),
                _buildStatusDisplay(status),
              ],
            ),
            subtitle: Text('Catatan: $remarks').small(),
          );
        },
      ),
    );
  }
}

class InfoRow extends StatelessWidget {
  final String label;
  final String value;
  final bool isBlue;

  const InfoRow(this.label, this.value, {super.key, this.isBlue = false});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(label).medium(),
          ),
          const Text(':').medium(),
          const SizedBox(width: paddingSmall),
          Expanded(
            child: isBlue ? Text(value).medium(color: Colors.blue) : Text(value).medium(),
          ),
        ],
      ),
    );
  }
}

class ValidationRow extends StatelessWidget {
  final String label;
  final String validationType;
  final String? value;
  final bool isDisabled;
  final Function(String?) onChanged;

  const ValidationRow({
    super.key,
    required this.label,
    required this.validationType,
    required this.value,
    required this.onChanged,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingMedium),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Label on the left
          SizedBox(
            width: 150,
            child: Text(label).medium(),
          ),
          const Text(':').medium(),
          const SizedBox(width: paddingSmall),

          // Dropdown on the right
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(radius),
              ),
              padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: value,
                  isExpanded: true,
                  hint: const Text('Pilih'),
                  onChanged: isDisabled ? null : onChanged,
                  items: [
                    // Add a null option for "Not selected"
                    const DropdownMenuItem<String>(
                      value: null,
                      child: Text('Pilih', style: TextStyle(color: Colors.grey)),
                    ),
                    ...ValidationConstants.getDropdownItems(validationType),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
