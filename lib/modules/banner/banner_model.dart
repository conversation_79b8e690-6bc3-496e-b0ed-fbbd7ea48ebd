import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:superapps_cms/controllers/profile_component_controller.dart';
import 'package:superapps_cms/models/base_model.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/extension.dart';
import 'package:superapps_cms/utils/field_config.dart';
import 'package:superapps_cms/utils/utils.dart';

class BannerModel extends BaseModel {
  final int id;
  final String title;
  final String content;
  final String frontImage;
  final String bgImage;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final int ordering;
  final String visibility;
  final String accessLevel;
  final DateTime createdAt;
  final DateTime updatedAt;

  BannerModel({
    required this.id,
    required this.title,
    required this.content,
    required this.frontImage,
    required this.bgImage,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    required this.ordering,
    required this.visibility,
    required this.accessLevel,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  @override
  List<FieldConfig> getFieldConfigs() {
    return [
      FieldConfig(
        key: 'id',
        label: 'ID',
        type: FieldType.number,
        editableOnlyOnCreate: false,
        skipOnUpdate: true,
        hideOnCreate: true,
        readOnly: true,
      ),
      FieldConfig(
        key: 'title',
        label: 'Nama Banner',
        type: FieldType.text,
        required: true,
        showInColumn: true,
      ),
      FieldConfig(
        key: 'content',
        label: 'Content',
        type: FieldType.richText,
        required: true,
      ),
      FieldConfig(
        key: 'frontImage',
        label: 'Image Utama',
        type: FieldType.image,
        required: true,
        showInColumn: true,
        tag: tagImageFlyer,
        customColumnWidget: (p0) => columnImage(p0, imageTag: tagImageFlyer),
      ),
      FieldConfig(
        key: 'bgImage',
        label: 'Image Next',
        type: FieldType.image,
        required: true,
        showInColumn: true,
        tag: tagImageFlyer,
        customColumnWidget: (p0) => columnImage(p0, imageTag: tagImageFlyer),
      ),
      if (Get.find<ProfileComponentController>().determineChannelAgeBan() == "")
        FieldConfig(
          key: 'accessLevel',
          label: 'Type Banner',
          type: FieldType.dropdown,
          options: optionAccessLevelCorporate,
          required: true,
          enableFilter: true,
          showInColumn: true,
          customColumnWidget: (p0) {
            return Text(optionAccessLevelCorporate[p0]).medium();
          },
        ),
      if (Get.find<ProfileComponentController>().determineChannelAgeBan() == "AGE")
        FieldConfig(
          key: 'accessLevel',
          label: 'Type Banner',
          type: FieldType.dropdown,
          options: {"AGE": "AGE"},
          required: true,
          enableFilter: false,
          hideOnCreate: true,
          hideOnUpdate: true,
          forceSendOnCreate: true,
          forceSendOnUpdate: true,
          showInColumn: false,
          customColumnWidget: (p0) {
            return Text(optionAccessLevelCorporate[p0]).medium();
          },
        ),
      if (Get.find<ProfileComponentController>().determineChannelAgeBan() == "BAN")
        FieldConfig(
          key: 'accessLevel',
          label: 'Type Banner',
          type: FieldType.dropdown,
          options: {"ALL": "Corporate", "BAN": "BAN"},
          required: true,
          enableFilter: true,
          hideOnCreate: true,
          hideOnUpdate: true,
          forceSendOnCreate: true,
          forceSendOnUpdate: true,
          showInColumn: true,
          customColumnWidget: (p0) {
            return Text(optionAccessLevelCorporate[p0]).medium();
          },
        ),
      FieldConfig(
        key: 'startDate',
        label: 'Start Date',
        type: FieldType.date,
        required: true,
        showInColumn: true,
        enableFilter: true,
        minDate: DateTime.now(),
        customColumnWidget: (value) => columnDate(value),
      ),
      FieldConfig(
        key: 'endDate',
        label: 'End Date',
        type: FieldType.date,
        required: true,
        showInColumn: true,
        enableFilter: true,
        minDate: DateTime.now().add(const Duration(days: 1)),
        customColumnWidget: (value) => columnDate(value),
      ),
      // Buat add & update
      FieldConfig(
        key: 'isActive',
        label: 'Active',
        type: FieldType.checkbox,
        required: true,
        showInColumn: true,
        enableFilter: false,
        options: optionActive,
        customColumnWidget: (value) => columnActive(value),
      ),
      // For filter purpose
      FieldConfig(
        key: 'isActive',
        label: 'Active',
        type: FieldType.checkbox,
        required: true,
        showInColumn: false,
        enableFilter: true,
        options: optionActiveBool,
        hideOnCreate: true,
        hideOnUpdate: true,
      ),
      
      FieldConfig(
        key: 'ordering',
        label: 'Order',
        type: FieldType.number,
        required: true,
        showInColumn: true,
      ),
      FieldConfig(
        key: 'visibility',
        label: 'Visibility',
        type: FieldType.dropdown,
        options: {"RESTRICTED": "Authenticated"},
        required: true,
        defaultValue: "RESTRICTED",
        hideOnUpdate: true,
        hideOnCreate: true,
        forceSendOnCreate: true,
        forceSendOnUpdate: true,
        enableFilter: false,
        showInColumn: false,
      ),
      FieldConfig(
        key: 'createdAt',
        label: 'Created At',
        type: FieldType.date,
        required: true,
        showInColumn: false,
        hideOnCreate: true,
        skipOnUpdate: true,
        readOnly: true,
      ),
      FieldConfig(
        key: 'updatedAt',
        label: 'Updated At',
        type: FieldType.date,
        required: true,
        showInColumn: false,
        hideOnCreate: true,
        readOnly: true,
        skipOnUpdate: true,
      ),
    ];
  }

  @override
  Map<String, String> getColumns() => Map.fromEntries(getFieldConfigs()
      .where((field) => field.showInColumn)
      .map((field) => MapEntry(field.key, field.label.isEmpty ? field.key : field.label)));

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'frontImage': frontImage,
      'bgImage': bgImage,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isActive': isActive,
      'ordering': ordering,
      'visibility': visibility,
      'accessLevel': accessLevel,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  static BannerModel empty() {
    return BannerModel(
      id: 0,
      title: '',
      content: '',
      frontImage: '',
      bgImage: '',
      startDate: DateTime.now(),
      endDate: DateTime.now().add(const Duration(days: 7)),
      isActive: false,
      ordering: 0,
      visibility: 'PUBLIC',
      accessLevel: 'BAN',
    );
  }

  @override
  BaseModel fromJson(Map<String, dynamic> json) {
    return BannerModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      frontImage: json['frontImage'] ?? '',
      bgImage: json['bgImage'] ?? '',
      startDate: json['startDate'] != null ? DateTime.parse(json['startDate']) : DateTime.now(),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : DateTime.now(),
      isActive: json['isActive'] ?? false,
      ordering: json['ordering'] ?? 0,
      visibility: json['visibility'] ?? 'PUBLIC',
      accessLevel: json['accessLevel'] ?? 'BAN',
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }
}
