import 'package:flutter/material.dart';
import 'package:superapps_cms/modules/approval/approval_controller.dart';
import 'package:superapps_cms/modules/approval/approval_model.dart';
import 'package:superapps_cms/models/base_model.dart';
import 'package:superapps_cms/routes/app_routes.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/extension.dart';
import 'package:superapps_cms/utils/utils.dart';
import 'package:get/get.dart';

/// CAS Review Profile BAN Controller
///
/// Extends the base ApprovalController to provide specific functionality
/// for the CAS Review Profile BAN page.
class CasReviewProfileBanController extends ApprovalController {
  @override
  String get endpoint {
    // Check if current status is BARU or MENUNGGU_PERSETUJUAN
    final currentStatus = selectedFilters['approvalStatus'] ?? '';
    if (currentStatus == STATUS_BARU || currentStatus == STATUS_MENUNGGU_PERSETUJUAN) {
      return apiAgent.approval;
    } else {
      return '${apiAgent.approval}/history';
    }
  }

  @override
  bool get debug => false;

  @override
  bool get enableCreate => false;

  @override
  bool get enableDelete => false;

  @override
  bool get enableSearch => true;

  @override
  BaseModel get model => ApprovalModel.empty();

  @override
  String get title => "Review Profile";

  @override
  void onInit() {
    super.onInit();

    // Set default filter for reviewer role
    final profileController = getProfileController();
    if (profileController.isCasReviewerBan()) {
      // Default filter for reviewers: show new items
      updateFilter('approvalStatus', STATUS_BARU);
    }

    // Check if we need to refresh data
    final arguments = Get.arguments;
    if (arguments != null && arguments['refresh'] == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        loadData();
      });
    }
  }

  @override
  String get trxType => "EDIT_PROFILE_BAN";

  @override
  void clickDetails(key) {
    // Construct the correct route based on context
    String detailRoute = Routes.REVIEW_BAN_PROFILE_DETAIL.replaceAll(':id', key);

    Get.toNamed(detailRoute)?.then((result) {
      // Refresh data when returning from detail page
      if (result == 'refresh') {
        loadData();
      }
    });
  }

  // Filter dialog variables
  final RxString selectedStatus = STATUS_BARU.obs;
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Apply filters
  void applyFilters() {
    // Clear existing filters
    selectedFilters.clear();

    // Apply status filter
    selectedFilters['approvalStatus'] = selectedStatus.value;
    selectedFilters['trxType'] = 'EDIT_PROFILE_BAN';

    // Apply date filters
    if (startDate.value != null) {
      selectedFilters['startDate'] = startDate.value!.formatToString(format: dateBackendShort);
    }
    if (endDate.value != null) {
      selectedFilters['endDate'] = endDate.value!.formatToString(format: dateBackendShort);
    }

    // Reload data
    currentPage.value = 0;
    loadData();
  }
}
