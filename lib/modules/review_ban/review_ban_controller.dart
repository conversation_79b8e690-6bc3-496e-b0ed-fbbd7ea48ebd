import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:superapps_cms/modules/unified_recruitment/unified_recruitment_page.dart';
import 'package:superapps_cms/modules/unified_rejoin/unified_rejoin_page.dart';
import 'package:superapps_cms/modules/unified_terminasi/unified_terminasi_page.dart';
import 'package:superapps_cms/modules/cas_review_profile_ban/cas_review_profile_ban_page.dart';
import 'package:superapps_cms/modules/cas_review_profile_ban/cas_review_profile_ban_controller.dart';
import 'package:superapps_cms/routes/app_routes.dart';

/// Review Ban Controller
///
/// Manages the review-ban page with 4 tabs:
/// - Perubahan data (Profile changes)
/// - Recruitment
/// - Rejoin
/// - Terminasi
class ReviewBanController extends GetxController with GetSingleTickerProviderStateMixin {
  // Tab controller
  late TabController tabController;
  final RxInt currentTabIndex = 0.obs;

  // Tab labels and URL mappings
  final List<String> tabs = [
    'Perubahan data'
    // , 'Rek<PERSON>t Kandidat', 'Rejoin', 'Terminasi'
  ];
  final List<String> tabUrls = [
    'profile'
    // , 'recruitment', 'rejoin', 'terminasi'
  ];

  // Tab content widgets - created once to avoid controller conflicts
  late final Widget profilePage;
  late final Widget recruitmentPage;
  late final Widget rejoinPage;
  late final Widget terminasiPage;

  @override
  void onInit() {
    super.onInit();
    _initializePages();
    _initializeTabs();
    _setInitialTabFromUrl();
  }

  @override
  void onReady() {
    super.onReady();
    // Check if we need to refresh data when page is ready
    final arguments = Get.arguments;
    if (arguments != null && arguments['refresh'] == true) {
      // Refresh the current tab data
      _refreshCurrentTabData();
    }
  }

  void _refreshCurrentTabData() {
    // Refresh data for the current tab
    switch (currentTabIndex.value) {
      case 0:
        // Profile tab - refresh profile data
        if (profilePage is CasReviewProfileBan) {
          // Trigger refresh on the profile controller
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // Find and refresh the profile controller
            try {
              final controller = Get.find<CasReviewProfileBanController>();
              controller.loadData();
            } catch (e) {
              // Controller not found, ignore
            }
          });
        }
        break;
      // Add other cases if needed for recruitment, rejoin, terminasi
    }
  }

  void _initializePages() {
    profilePage = CasReviewProfileBan();
    // recruitmentPage = UnifiedRecruitmentPage();
    // rejoinPage = UnifiedRejoinPage();
    // terminasiPage = UnifiedTerminasiPage();
  }

  void _initializeTabs() {
    tabController = TabController(
      initialIndex: 0,
      length: tabs.length,
      vsync: this,
    );

    tabController.addListener(() {
      currentTabIndex.value = tabController.index;
    });
  }

  // Switch to specific tab
  void switchTab(int index) {
    if (index >= 0 && index < tabs.length) {
      tabController.animateTo(index);
      currentTabIndex.value = index;
      _updateUrl(index);
    }
  }

  // Get page widget for current tab
  Widget getCurrentTabContent() {
    switch (currentTabIndex.value) {
      case 0:
        // Perubahan data - Profile changes
        return profilePage;
      case 1:
        // Recruitment
        return recruitmentPage;
      case 2:
        // Rejoin
        return rejoinPage;
      case 3:
        // Terminasi
        return terminasiPage;
      default:
        return profilePage;
    }
  }

  // Set initial tab based on URL parameter
  void _setInitialTabFromUrl() {
    final tabParam = Get.parameters['tab'];
    if (tabParam != null) {
      final tabIndex = tabUrls.indexOf(tabParam);
      if (tabIndex >= 0) {
        currentTabIndex.value = tabIndex;
        tabController.animateTo(tabIndex);
      }
    } else {
      // Check if we're coming from a detail page by looking at the current route
      final currentRoute = Get.currentRoute;

      // If we're coming from a detail page, don't redirect - just stay on current tab
      if (currentRoute.contains(Routes.REVIEW_BAN) &&
          (currentRoute.contains('/rejoin/') ||
              currentRoute.contains('/terminasi/') ||
              currentRoute.contains('/recruitment/') ||
              currentRoute.contains('/profile/'))) {
        // We're in a detail page, don't redirect
        return;
      }

      // Only redirect to default tab if we're actually on the base review-ban route
      if (currentRoute == Routes.REVIEW_BAN) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final newUrl = '${Routes.REVIEW_BAN}/${tabUrls[0]}'; // Default to first tab (profile)
          Get.toNamed(newUrl, preventDuplicates: false);
        });
      }
    }
  }

  // Update URL when tab changes
  void _updateUrl(int index) {
    if (index >= 0 && index < tabUrls.length) {
      final newUrl = '${Routes.REVIEW_BAN}/${tabUrls[index]}';
      Get.toNamed(newUrl, preventDuplicates: false);
    }
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
}
