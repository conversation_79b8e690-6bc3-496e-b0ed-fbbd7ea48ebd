import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:superapps_cms/controllers/profile_component_controller.dart';
import 'package:superapps_cms/modules/approval/approval_model.dart';
import 'package:superapps_cms/routes/app_routes.dart';
import 'package:superapps_cms/utils/base_controller.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/utils.dart';
import 'package:toastification/toastification.dart';

class FieldApprovalItem {
  final String fieldName;
  final String displayName;
  final String oldValue;
  final String newValue;
  final RxBool approved;
  final TextEditingController remarksController;
  final String? attachmentUrl;
  final bool requiresAttachment;

  FieldApprovalItem({
    required this.fieldName,
    required this.displayName,
    required this.oldValue,
    required this.newValue,
    bool initialApprovalState = true,
    this.attachmentUrl,
    this.requiresAttachment = false,
  })  : approved = initialApprovalState.obs,
        remarksController = TextEditingController();

  Map<String, dynamic> toJson() => {
        'fieldName': fieldName,
        'status': approved.value ? 'DISETUJUI' : 'DITOLAK',
        'remarks': remarksController.text,
      };
}

class ApprovalDetailsController extends BaseController {
  final String id;
  final Rx<ApprovalModel?> approvalData = Rx<ApprovalModel?>(null);
  final RxList<FieldApprovalItem> fieldItems = RxList<FieldApprovalItem>([]);
  final RxBool allApproved = true.obs;
  final TextEditingController generalRemarksController = TextEditingController();
  final RxBool finalApproval = true.obs;

  ApprovalDetailsController(this.id);

  @override
  void onInit() {
    super.onInit();
    loadApprovalDetails();
  }

  // Add a property to track if actions should be disabled
  final RxBool actionsDisabled = false.obs;
  final RxBool isCurrentUserApprover = false.obs;
  final RxBool isProfileLoading = true.obs;

  Future<void> loadApprovalDetails() async {
    isLoading.value = true;
    isProfileLoading.value = true;

    // Load approval data
    final response = await apiAgent.get('${apiAgent.approval}/$id');

    if (response.isOk) {
      approvalData.value = ApprovalModel.empty().fromJson(response.body);

      // Parse field data first
      _parseFieldData();

      // After parsing field data, check if there are any items to approve
      if (fieldItems.isEmpty) {
        actionsDisabled.value = true;
      }

      // Check profile controller in a separate async function to avoid blocking
      _checkUserApprovalRights();
    } else {
      toast(text: "Failed to load approval details", type: ToastificationType.error);
      isProfileLoading.value = false;
    }

    isLoading.value = false;
  }

  Future<void> _checkUserApprovalRights() async {
    try {
      // Get the profile controller using the utility function
      final profileController = getProfileController();

      // Wait for profile to be loaded if it's still loading
      if (profileController.isLoading.value) {
        // Set a listener to be notified when profile loading is complete
        ever(profileController.isLoading, (loading) {
          if (!loading) {
            // Profile loading is complete, now check approval rights
            _updateApprovalRights(profileController);
          }
        });
      } else {
        // Profile is already loaded, check approval rights immediately
        _updateApprovalRights(profileController);
      }
    } catch (e) {
      logger.e('Error checking user approval rights: $e');
      actionsDisabled.value = true;
      isProfileLoading.value = false;
    }
  }

  void _updateApprovalRights(ProfileComponentController profileController) {
    if (approvalData.value != null) {
      final String approverRole = approvalData.value!.approverRole;

      // Check if the current user is the approver based on the required role
      if ([ROLE_CAS_REVIEW_AGE, ROLE_CAS_REVIEW_BAN].contains(approverRole) && profileController.isCasReviewerAge()) {
        isCurrentUserApprover.value = true;
      } else if ([ROLE_CAS_APPROVAL_AGE, ROLE_CAS_APPROVAL_BAN].contains(approverRole) &&
          profileController.isCasApprovalAge()) {
        isCurrentUserApprover.value = true;
      } else {
        isCurrentUserApprover.value = false;
      }

      // Disable actions if:
      // 1. The user is not the current approver, or
      // 2. The approval status is not "BARU" or "MENUNGGU_PERSETUJUAN"
      if (!isCurrentUserApprover.value ||
          (approvalData.value!.approvalStatus != 'MENUNGGU_PERSETUJUAN' &&
              approvalData.value!.approvalStatus != 'BARU')) {
        actionsDisabled.value = true;
      } else {
        actionsDisabled.value = false;
      }
    }

    // Profile loading is complete
    isProfileLoading.value = false;
  }

  void _parseFieldData() {
    if (approvalData.value == null) return;

    try {
      final String? dataString = approvalData.value!.detailData['data'];
      if (dataString == null || dataString.isEmpty) {
        logger.e('No data field found in approval data');
        return;
      }

      final Map<String, dynamic> fieldsData = jsonDecode(dataString);
      fieldItems.clear();

      final Map<String, String> fieldDisplayNames = {
        'photo': 'Foto', // Add photo field mapping
        'photoUrl': 'Foto', // Add photoUrl field mapping
        'agentName': 'Nama Agen',
        'name': 'Nama Lengkap',
        'email': 'Email',
        'phoneNumber': 'Nomor Telepon',
        'address': 'Alamat',
        'level': 'Level',
        'status': 'Status',
        'birthDate': 'Tanggal Lahir',
        'identityNumber': 'Nomor Identitas',
        'npwp': 'NPWP',
        'bank': 'Bank',
        'bankAccount': 'Nomor Rekening',
        'bankAccountName': 'Nama Pemilik Rekening',
        'maritalStatus': 'Marital Status'
        // Add other field mappings as needed
      };

      // Extract attachment URLs if they exist in the data
      String? ktpAttachmentUrl = fieldsData['ktpAttachment']?.toString();
      String? bankAttachmentUrl = fieldsData['bankAttachment']?.toString();
      String? kkAttachmentUrl = fieldsData['kkAttachment']?.toString();
      String? photoAttachmentUrl = fieldsData['photo']?.toString() ?? fieldsData['photoUrl']?.toString();

      // Parse oldData if available
      Map<String, dynamic>? oldDataMap;
      if (approvalData.value?.oldData != null && approvalData.value!.oldData!.isNotEmpty) {
        try {
          oldDataMap = jsonDecode(approvalData.value!.oldData!);
        } catch (e) {
          logger.e('Error parsing oldData: $e');
          // If oldData parsing fails, we'll fall back to detailData
        }
      }

      // Parse existing approval details to get current field approval states
      Map<String, Map<String, dynamic>> existingApprovals = _parseExistingApprovals();

      fieldsData.forEach((fieldName, fieldValue) {
        // Skip attachment fields
        if (['ktpAttachment', 'bankAttachment', 'kkAttachment'].contains(fieldName)) return;

        String oldValue;
        String newValue;

        // Determine old value - prioritize oldData over detailData
        if (oldDataMap != null && oldDataMap.containsKey(fieldName)) {
          // Use value from oldData
          oldValue = oldDataMap[fieldName]?.toString() ?? '-';
        } else {
          // Fall back to current logic using detailData
          if (fieldName == 'photo' || fieldName == 'photoUrl') {
            oldValue = approvalData.value?.detailData['agent']?['photoUrl']?.toString() ??
                approvalData.value?.detailData['agent']?['photo']?.toString() ??
                '-';
          } else {
            oldValue = approvalData.value?.detailData['agent']?[fieldName]?.toString() ?? '-';
          }
        }

        // Determine new value (this logic remains the same)
        if (fieldName == 'photo' || fieldName == 'photoUrl') {
          newValue = fieldValue?.toString() ?? '-';
          // If we have a photo attachment, use it as the new value
          if (photoAttachmentUrl != null && photoAttachmentUrl.isNotEmpty) {
            newValue = photoAttachmentUrl;
          }
        } else {
          newValue = fieldValue?.toString() ?? '-';
        }

        final String displayName = fieldDisplayNames[fieldName] ?? fieldName;

        // Determine if this field requires attachment
        bool requiresAttachment = false;
        String? attachmentUrl;

        // For photo fields, always show the comparison
        if (fieldName == 'photo' || fieldName == 'photoUrl') {
          requiresAttachment = false;
          attachmentUrl = photoAttachmentUrl;
        }
        // For agentName/name fields, use KTP attachment
        else if (fieldName == 'agentName' || fieldName == 'name') {
          requiresAttachment = true;
          attachmentUrl = ktpAttachmentUrl;
        }
        // For bank-related fields, use bank attachment
        else if (fieldName == 'bank' || fieldName == 'bankAccount' || fieldName == 'bankAccountName') {
          requiresAttachment = true;
          attachmentUrl = bankAttachmentUrl;
        }
        // For marital status fields, use KK attachment
        else if (fieldName == 'maritalStatus') {
          requiresAttachment = true;
          attachmentUrl = kkAttachmentUrl;
        }

        // Check if there's an existing approval for this field
        bool initialApprovalState = true;
        String initialRemarks = '';

        if (existingApprovals.containsKey(fieldName)) {
          final existingApproval = existingApprovals[fieldName]!;
          initialApprovalState = existingApproval['action'] == 'DISETUJUI';
          initialRemarks = existingApproval['remark'] ?? '';
        }

        final fieldItem = FieldApprovalItem(
          fieldName: fieldName,
          displayName: displayName,
          oldValue: oldValue,
          newValue: newValue,
          requiresAttachment: requiresAttachment,
          attachmentUrl: attachmentUrl,
          initialApprovalState: initialApprovalState,
        );

        // Set initial remarks if there's an existing approval
        if (initialRemarks.isNotEmpty) {
          fieldItem.remarksController.text = initialRemarks;
        }

        fieldItems.add(fieldItem);
      });

      if (fieldItems.isEmpty) {
        logger.e('No fields found in the data string for approval');
      }
    } catch (e) {
      logger.e('Error parsing field data: $e');
      toast(text: "Error parsing approval data", type: ToastificationType.error);
    }
  }

  /// Parse existing approval details from the API response
  Map<String, Map<String, dynamic>> _parseExistingApprovals() {
    Map<String, Map<String, dynamic>> approvals = {};

    try {
      // First check the main detailApproval field
      if (approvalData.value!.detailApproval.isNotEmpty) {
        final List<dynamic> mainApprovals = jsonDecode(approvalData.value!.detailApproval);
        for (var approval in mainApprovals) {
          final String field = approval['field'] ?? '';
          if (field.isNotEmpty) {
            approvals[field] = {
              'action': approval['action'] ?? 'DISETUJUI',
              'remark': approval['remark'] ?? '',
            };
          }
        }
      }

      // Then check approval details from the approval history
      for (var detail in approvalData.value!.approvalDetails) {
        if (detail.detailApproval.isNotEmpty) {
          final List<dynamic> detailApprovals = jsonDecode(detail.detailApproval);
          for (var approval in detailApprovals) {
            final String field = approval['field'] ?? '';
            if (field.isNotEmpty) {
              // Use the most recent approval (override if exists)
              approvals[field] = {
                'action': approval['action'] ?? 'DISETUJUI',
                'remark': approval['remark'] ?? '',
              };
            }
          }
        }
      }
    } catch (e) {
      logger.e('Error parsing existing approvals: $e');
    }

    return approvals;
  }

  void toggleAllApproval(bool value) {
    allApproved.value = value;
    for (var item in fieldItems) {
      item.approved.value = value;
    }
  }

  bool validateApproval() {
    // Check if any rejected field is missing remarks
    for (var item in fieldItems) {
      if (!item.approved.value && item.remarksController.text.isEmpty) {
        toast(text: "Alasan harus diisi untuk setiap field yang ditolak", type: ToastificationType.error);
        return false;
      }
    }
    return true;
  }

  // Navigate back to the appropriate list page
  void goBack() {
    // Get the current route
    final currentRoute = Get.currentRoute;

    // Extract the base route (without the ID)
    String baseRoute = '';

    if (currentRoute.contains(Routes.CAS_APPROVAL_PROFILE_DETAIL.replaceAll("/:id", ""))) {
      baseRoute = Routes.CAS_APPROVAL_PROFILE;
    } else if (currentRoute.contains('/review-age/profile')) {
      baseRoute = Routes.REVIEW_AGE_TAB.replaceAll(':tab', 'profile');
    } else if (currentRoute.contains('/approval-age/profile')) {
      baseRoute = Routes.APPROVER_AGE_TAB.replaceAll(':tab', 'profile');
    } else if (currentRoute.contains('/review-ban/profile')) {
      baseRoute = Routes.REVIEW_BAN_TAB.replaceAll(':tab', 'profile');
    } else if (currentRoute.contains('/approval-ban/profile')) {
      baseRoute = Routes.APPROVER_BAN_TAB.replaceAll(':tab', 'profile');
    }

    if (Get.previousRoute == baseRoute) {
      Get.back(result: 'refresh');
    } else {
      // Navigate to the base route and trigger refresh
      Get.offNamed(baseRoute, arguments: {'refresh': true});
    }
  }

  Future<void> submitApproval() async {
    if (!validateApproval()) {
      return;
    }

    isUploading.value = true;

    // Build field approvals data array
    final List<Map<String, dynamic>> fieldApprovals = fieldItems
        .map((item) => {
              "field": item.fieldName,
              "action": item.approved.value ? "DISETUJUI" : "DITOLAK",
              "remark": item.remarksController.text
            })
        .toList();

    // Determine overall action and remarks
    String overallAction = "DISETUJUI";
    String overallRemarks = "";

    // Check if any field is rejected
    for (var item in fieldItems) {
      if (!item.approved.value) {
        overallAction = "DITOLAK";
        // Use the first rejected field's remarks
        overallRemarks = item.remarksController.text;
        break;
      }
    }

    final payload = {
      "approvalHeaderId": int.parse(id),
      "action": overallAction,
      "approvalDetails": fieldApprovals,
      "remarks": overallRemarks,
    };

    try {
      final response = await apiAgent.post(apiAgent.approval, payload);

      if (response.isOk) {
        toast(text: "Approval successfully submitted", type: ToastificationType.success);
        goBack(); // Use our custom navigation method
      } else {
        // Show error in dialog instead of toast
        _showErrorDialog(response);
      }
    } catch (e) {
      // Show error in dialog instead of toast
      _showErrorDialog(null, errorMessage: e.toString());
    } finally {
      isUploading.value = false;
    }
  }

  void _showErrorDialog(Response? response, {String? errorMessage}) {
    String errorText = "An error occurred";

    if (response != null && response.body != null) {
      // Try to extract error_description from response body
      try {
        if (response.body is Map<String, dynamic>) {
          errorText =
              response.body['error_description'] ?? response.body['message'] ?? response.statusText ?? errorText;
        } else if (response.body is String) {
          errorText = response.body;
        } else {
          errorText = response.statusText ?? errorText;
        }
      } catch (e) {
        errorText = response.statusText ?? errorText;
      }
    } else if (errorMessage != null) {
      errorText = errorMessage;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('Error'),
        content: SelectableText(errorText),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  @override
  void onClose() {
    for (var item in fieldItems) {
      item.remarksController.dispose();
    }
    super.onClose();
  }
}
