import 'package:get/get.dart';
import 'package:superapps_cms/controllers/branch_component_controller.dart';
import 'package:superapps_cms/controllers/city_component_controller.dart';
import 'package:superapps_cms/controllers/role_component_controller.dart';
import 'package:superapps_cms/modules/agent/agent_model.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/field_config.dart';
import 'package:superapps_cms/utils/utils.dart';

class AgentBanModel extends AgentModel {
  AgentBanModel({
    required super.id,
    required super.leaderCode,
    required super.agentCode,
    required super.agentName,
    required super.distributionCode,
    required super.roleName,
    required super.level,
    required super.positionLevel,
    required super.regionCode,
    required super.regionName,
    required super.subRegionCode,
    required super.subRegionName,
    required super.areaCode,
    required super.areaName,
    required super.branchCode,
    required super.branchName,
    required super.groupCode,
    required super.groupName,
    required super.licenseNumberAAJI,
    required super.licenseNumberAASI,
    required super.leaderG2G,
    required super.recruiterCode,
    required super.gender,
    required super.education,
    required super.status,
    required super.channel,
    required super.email,
    required super.phoneNumber,
    required super.address,
    required super.bankAccountNumber,
    required super.bank,
    required super.maritalStatus,
    required super.bankAttachment,
    required super.ktpAttachment,
    required super.kkAttachment,
    required super.photo,
    required super.mbranchCode,
    required super.mbranchName,
    required super.sbranchCode,
    required super.sbranchName,
    required super.leaderName,
    required super.roles,
    required super.branches,
  });

  @override
  List<FieldConfig> getFieldConfigs() {
    return [
      FieldConfig(
        key: 'id',
        label: 'ID',
        type: FieldType.number,
        readOnly: true,
        skipOnUpdate: true,
        hideOnCreate: true,
      ),
      FieldConfig(
        key: 'agentName',
        label: 'Name',
        type: FieldType.text,
        required: true,
        showInColumn: true,
        readOnly: true,
        enableSearch: true,
      ),
      FieldConfig(
        key: 'agentCode',
        label: 'Agent Code',
        type: FieldType.text,
        required: true,
        showInColumn: true,
        readOnly: true,
        skipOnUpdate: true,
        hideOnCreate: false,
        enableSearch: true,
      ),
      FieldConfig(
        key: 'email',
        label: 'Email',
        type: FieldType.text,
        required: false,
        readOnly: true,
        showInColumn: true,
        skipOnUpdate: true,
        enableSearch: true,
      ),
      FieldConfig(
        key: 'leaderCode',
        label: 'Leader Code',
        type: FieldType.text,
        required: false,
        skipOnUpdate: true,
        readOnly: true,
      ),
      FieldConfig(
        key: 'roles',
        label: 'Roles',
        readOnly: true,
        type: FieldType.dropdownSearchMulti,
        enableFilter: false,
        options: Get.find<RoleComponentController>().dropdownItemsBan,
        required: true,
        showInColumn: true,
        customColumnWidget: (p0) => columnRoles(p0),
      ),
      FieldConfig(
        key: 'role',
        label: 'User Roles',
        readOnly: true,
        type: FieldType.dropdownSearchMulti,
        enableFilter: true,
        options: Get.find<RoleComponentController>().dropdownItemsBan,
        required: false,
        showInColumn: false,
        hideOnCreate: true,
        hideOnUpdate: true,
      ),
      FieldConfig(
        key: 'branchCode',
        label: 'Branch Code',
        type: FieldType.text,
        hideOnCreate: true,
        hideOnUpdate: true,
      ),
      FieldConfig(
        key: 'branchName',
        label: 'Branch Name',
        type: FieldType.text,
        hideOnCreate: true,
        hideOnUpdate: true,
      ),
      FieldConfig(
        key: 'channel',
        label: 'Channel',
        type: FieldType.dropdown,
        options: {"BAN": "BAN"},
        required: true,
        showInColumn: false,
        hideOnCreate: true,
        hideOnUpdate: true,
        forceSendOnCreate: true,
        forceSendOnUpdate: true,
        readOnly: true,
      ),
      // FieldConfig(
      //   key: 'mbranchCode',
      //   label: 'KCU',
      //   type: FieldType.dropdownSearchMulti,
      //   options: Get.find<BranchComponentController>().dropdownItemsBan,
      //   showInColumn: false,
      //   readOnly: true,
      // ),
      FieldConfig(
        key: 'mbranchName',
        label: 'KCU',
        type: FieldType.multiline,
        showInColumn: true,
        // hideOnCreate: true,
        // hideOnUpdate: true,
        readOnly: true,
      ),
      // FieldConfig(
      //   key: 'sbranchCode',
      //   label: 'KCP',
      //   type: FieldType.dropdownSearchMulti,
      //   options: Get.find<BranchComponentController>().dropdownItemsBan,
      //   readOnly: true,
      // ),
      FieldConfig(
        key: 'sbranchName',
        label: 'KCP',
        type: FieldType.multiline,
        showInColumn: true,
        // hideOnCreate: true,
        // hideOnUpdate: true,
        readOnly: true,
      ),
      FieldConfig(
        key: 'status',
        label: 'Status',
        type: FieldType.dropdown,
        options: optionAgentStatus,
        required: true,
        readOnly: true,
        showInColumn: true,
      ),
      FieldConfig(
        key: 'city',
        label: 'City',
        type: FieldType.dropdownSearch,
        options: Get.find<CityComponentController>().dropdownItems,
        required: false,
        enableFilter: false,
        hideOnCreate: true,
        hideOnUpdate: true,
        readOnly: true,
      ),
      FieldConfig(
        key: 'branch',
        label: 'KCU/KCP',
        type: FieldType.dropdownSearch,
        options: Get.find<BranchComponentController>().dropdownItems,
        required: false,
        enableFilter: true,
        hideOnCreate: true,
        hideOnUpdate: true,
        readOnly: true,
      ),
    ];
  }

  static AgentBanModel empty() {
    return AgentBanModel(
      id: 0,
      leaderCode: '',
      agentCode: '',
      agentName: '',
      distributionCode: 'A',
      roleName: '',
      level: '',
      positionLevel: '',
      regionCode: '',
      regionName: '',
      subRegionCode: '',
      subRegionName: '',
      areaCode: '',
      areaName: '',
      branchCode: '',
      branchName: '',
      groupCode: '',
      groupName: '',
      licenseNumberAAJI: '',
      licenseNumberAASI: '',
      leaderG2G: '',
      recruiterCode: '',
      gender: '',
      education: '',
      status: 'A',
      channel: 'BAN',
      email: '',
      phoneNumber: '',
      address: '',
      bankAccountNumber: '',
      bank: '',
      maritalStatus: '',
      bankAttachment: '',
      ktpAttachment: '',
      kkAttachment: '',
      photo: '',
      mbranchCode: '',
      mbranchName: '',
      sbranchCode: '',
      sbranchName: '',
      leaderName: '',
      roles: [],
      branches: [],
    );
  }
}
