import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:superapps_cms/common_components/loader.dart';
import 'package:superapps_cms/controllers/base_cms_controller.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/extension.dart';
import 'package:superapps_cms/utils/field_config.dart';
import 'package:superapps_cms/utils/utils.dart';
import 'package:toastification/toastification.dart';

/**
 * # Base CMS Page (`BaseCMSPage` class)
 *
 * ## Overview
 * `BaseCMSPage` provides the standard UI layout for CMS pages, displaying data tables
 * with search, filter, sort, and CRUD operations.
 *
 * ## Key Features
 *
 * - **Data Table**: Displays model data in a paginated, sortable table
 * - **Search Bar**: Global or field-specific search
 * - **Filters**: UI for filtering data
 * - **Action Buttons**: Add, edit, delete operations
 * - **Loading States**: Loading indicators during data fetch
 * - **Tab Support**: For multi-tab interfaces
 *
 * ## Key Methods
 *
 * - `sectionTitle()`: Renders the page title
 * - `sectionActions()`: Renders action buttons (search, filter, add)
 * - `sectionContent()`: Renders main content (table or tabs)
 * - `actionButtons()`: Hook for custom action buttons
 *
 * ## Example Implementation
 *
 * ```dart
 * class HospitalPage extends BaseCMSPage {
 *   HospitalPage({super.key}) : super(
 *     controller: Get.put(HospitalController(), tag: getRandomString())
 *   );
 *
 *   @override
 *   String get title => "Hospitals";
 *
 *   // Optional: Add custom action buttons
 *   @override
 *   Widget? actionButtons() {
 *     return TextButton(
 *       onPressed: () => // Custom action,
 *       child: const Text("Custom Action"),
 *     );
 *   }
 * }
 * ```
 */
abstract class BaseCMSPage<T extends BaseCMSController> extends StatelessWidget {
  final T controller;
  // RxBool to track if the filter/search bottom sheet is open
  final RxBool isBottomSheetOpen = false.obs;

  BaseCMSPage({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(paddingMedium),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          spacing: paddingSmall,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (sectionTitle() != null) sectionTitle()!,
            if (sectionActions() != null) sectionActions()!,
            Expanded(
              child: Obx(
                () => Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    if (controller.isLoading.isTrue)
                      Center(
                        child: Container(
                          padding: const EdgeInsets.all(paddingExtraLarge),
                          child: const Loader(),
                        ),
                      ),
                    if (controller.isLoading.isFalse)
                      Expanded(
                        child: SizedBox(
                          width: double.infinity,
                          child: sectionContent(),
                        ),
                      )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /**
   * Builds the action section of the page, including search, filters, and buttons.
   *
   * This section contains:
   * - Search field (if enableSearch is true)
   * - Filter controls (if enableFilter is true and filters exist)
   * - Add button (if enableCreate is true)
   * - Custom action buttons (if provided by actionButtons())
   *
   * @return A Row widget containing the action components
   */
  Widget? sectionActions() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Check if we're in mobile view (width < 600)
        final bool isMobile = constraints.maxWidth < 600;

        if (isMobile) {
          // Mobile view - show toggle button that opens bottom sheet
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: paddingSmall),
            child: Row(
              spacing: paddingSmall,
              children: [
                // Filter/Search toggle button
                if (controller.enableSearch || (controller.enableFilter && controller.filters.isNotEmpty))
                  Obx(() {
                    // Count active filters
                    int activeFilterCount = 0;
                    for (var key in controller.selectedFilters.keys) {
                      if (controller.filters.containsKey(key) &&
                          controller.filters[key]!.enableFilter &&
                          controller.filters[key] != null &&
                          controller.selectedFilters[key] != "") {
                        activeFilterCount++;
                      }

                      if (controller.searchController.text.isNotEmpty) {
                        activeFilterCount++;
                      }
                    }
                    // Show badge with filter count if there are active filters
                    return Badge(
                      isLabelVisible: activeFilterCount > 0,
                      label: Text(activeFilterCount.toString()),
                      child: IconButton(
                        icon: const Icon(Icons.filter_alt),
                        onPressed: () => _showMobileActionsSheet(context),
                        tooltip: 'Search & Filters',
                      ),
                    );
                  }),
                const Spacer(),
                // Add button
                if (controller.enableCreate)
                  ElevatedButton(
                    onPressed: () => controller.clickAddNew(),
                    child: const Text("Add"),
                  ),
                // Custom action buttons
                if (actionButtons() != null) actionButtons()!,
              ],
            ),
          );
        } else {
          // Desktop view - show full search and filter UI
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: paddingSmall),
            child: Row(
              children: [
                if (controller.enableSearch)
                  Expanded(
                    child: sectionLeft(needsExpanded: false),
                  ),
                if (controller.enableSearch) const SizedBox(width: paddingMedium),
                Row(
                  spacing: paddingSmall,
                  children: [
                    if ((controller.enableFilter && controller.filters.isNotEmpty)) sectionRight(),
                    customSectionRight(),
                    if (controller.enableCreate)
                      ElevatedButton(
                        onPressed: () => controller.clickAddNew(),
                        child: const Text("Add"),
                      ),
                    if (actionButtons() != null) actionButtons()!,
                  ],
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget customSectionRight() {
    return const SizedBox.shrink();
  }

  Widget sectionLeft({bool isMobile = false, bool needsExpanded = true}) {
    bool enableGlobalSearch = controller.searchFields.isEmpty;
    List<String> listSearchFields = controller.searchFields.values.map((item) => item.key).toList();

    Widget searchWidget = Row(
      children: [
        if (enableGlobalSearch)
          Expanded(
            child: TextField(
              decoration: const InputDecoration(
                hintText: "Search by key or content...",
                border: OutlineInputBorder(),
              ),
              controller: controller.searchController,
              onSubmitted: (value) => controller.search(),
            ),
          ),
        if (!enableGlobalSearch)
          Expanded(
            child: Container(
              width: isMobile ? double.infinity : null,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(radius),
                border: Border.all(
                  color: Colors.grey.withAlpha(5),
                ),
              ),
              clipBehavior: Clip.hardEdge,
              padding: const EdgeInsets.symmetric(horizontal: paddingSmall),
              child: Row(
                spacing: paddingSmall,
                children: [
                  Obx(
                    () => DropdownButton<String>(
                      items: listSearchFields
                          .map(
                            (item) => DropdownMenuItem<String>(
                              value: item,
                              child: Text(controller.searchFields[item]?.label ?? ''),
                            ),
                          )
                          .toList(),
                      onChanged: (item) {
                        controller.searchKeySelection.value = item ?? '';
                        controller.update();
                      },
                      value: controller.searchKeySelection.value.isNotEmpty &&
                              controller.searchFields.containsKey(controller.searchKeySelection.value)
                          ? controller.searchKeySelection.value
                          : controller.searchFields.values.first.key,
                      underline: Container(),
                      isExpanded: isMobile,
                    ),
                  ),
                  Expanded(
                    child: TextField(
                      decoration: const InputDecoration(
                        hintText: "Search by key or content...",
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                      ),
                      controller: controller.searchController,
                      onSubmitted: (value) => controller.search(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        IconButton(
          onPressed: () => controller.search(),
          icon: const Icon(Icons.search, size: 20),
        ),
      ],
    );

    return needsExpanded ? Expanded(child: searchWidget) : searchWidget;
  }

  Widget sectionRight({bool isMobile = false}) {
    return Obx(
      () {
        // Count active filters (fields with enableFilter=true that have values in selectedFilters)
        int activeFilterCount = 0;
        for (var key in controller.filters.keys) {
          if (controller.selectedFilters.containsKey(key) &&
              controller.selectedFilters[key] != null &&
              controller.selectedFilters[key]!.isNotEmpty) {
            activeFilterCount++;
          }
        }

        List<Widget> childrens = [
          ...controller.filters.keys.map((key) {
            final field = controller.filters[key]!;
            final value = controller.selectedFilters[key];

            // Base widget that shows the field label
            Widget labelWidget = Text(field.label).small(color: primaryColorLight);

            // Content widget based on field type
            Widget contentWidget;

            // Handle different field types
            switch (field.type) {
              case FieldType.dropdown:
              case FieldType.dropdownSearch:
              case FieldType.dropdownSearchMulti:
              case FieldType.checkbox:
                // For dropdown, show a dropdown button
                return _buildDropdown(field, key, value, isMobile: isMobile);

              case FieldType.date:
                // For date fields, show the selected date or a date picker button
                final displayText = value != null
                    ? DateTime.parse(value.toString()).formatToString(format: field.dateFormat ?? 'dd-MM-yyyy')
                    : 'Select date';

                contentWidget = Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(displayText).small(color: Colors.black),
                  ],
                );

                return SizedBox(
                  width: isMobile ? double.infinity : null,
                  child: TextButton(
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: paddingSmall, vertical: paddingExtraSmall),
                    ),
                    onPressed: () async {
                      final DateTime? picked = await showDatePicker(
                        context: Get.context!,
                        initialDate:
                            value != null ? value.toString().formatToDatetime() ?? DateTime.now() : DateTime.now(),
                        firstDate: DateTime(1900),
                        lastDate: DateTime(2100),
                      );
                      if (picked != null) {
                        controller.updateFilter(
                          key,
                          picked.formatToString(format: dateBackendShort),
                        );
                      }
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        labelWidget,
                        contentWidget,
                      ],
                    ),
                  ),
                );

              default:
                // For other field types, show a simple filter button
                if (value == null) {
                  contentWidget = Text(field.label).medium();
                } else {
                  contentWidget = Text(value.toString()).medium();
                }

                return SizedBox(
                  width: isMobile ? double.infinity : null,
                  child: TextButton(
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: paddingSmall, vertical: paddingExtraSmall),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(radius),
                        side: BorderSide(color: Colors.grey.shade300),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (value != null) labelWidget,
                        contentWidget,
                      ],
                    ),
                    onPressed: () => controller.toggleFilter(key),
                  ),
                );
            }
          }),

          // Add clear filter button if there are active filters
          if (activeFilterCount >= 1)
            SizedBox(
              width: isMobile ? double.infinity : null,
              child: TextButton.icon(
                onPressed: () => controller.clearAllFilters(),
                icon: const Icon(Icons.clear_all, size: 18, color: Colors.red),
                label: const Text('Clear filters').small(color: Colors.red),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: paddingSmall, vertical: paddingExtraSmall),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(radius),
                    side: BorderSide(color: Colors.red.shade300, width: 1),
                  ),
                ),
              ),
            ),
        ];

        if (isMobile) {
          return Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(radius),
              ),
              child: SingleChildScrollView(
                child: Column(
                  spacing: paddingMedium,
                  children: childrens,
                ),
              ),
            ),
          );
        }

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(radius),
          ),
          child: Row(
            spacing: paddingSmall,
            children: childrens,
          ),
        );
      },
    );
  }

  Widget _buildDropdown(FieldConfig field, String key, String? value, {bool isMobile = false}) {
    return SizedBox(
      width: isMobile ? double.infinity : 120,
      height: 55,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: paddingExtraSmall,
        children: [
          Text(
            field.hint ?? field.label,
          ).small(
            color: textColorSecondary,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Expanded(
            child: DropdownSearch<String>(
              items: (filter, infiniteScrollProps) =>
                  field.options?.entries
                      .map(
                        (entry) => entry.value,
                      )
                      .toList() ??
                  [],
              dropdownBuilder: (context, item) => Text(
                item ?? '',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ).small(color: textColorPrimary),
              suffixProps: const DropdownSuffixProps(
                dropdownButtonProps: DropdownButtonProps(
                  iconClosed: Icon(Icons.keyboard_arrow_down),
                  iconOpened: Icon(Icons.keyboard_arrow_up),
                ),
              ),
              popupProps: PopupProps.menu(
                showSearchBox: true,
                searchFieldProps: TextFieldProps(
                  decoration: InputDecoration(
                    hintText: 'Search...',
                    contentPadding: const EdgeInsets.symmetric(horizontal: paddingSmall, vertical: paddingSmall),
                    border: const OutlineInputBorder(),
                    hintStyle: bodySmallStyleLight.copyWith(
                      color: textColorSecondary,
                    ),
                    isDense: true,
                  ),
                  style: bodySmallStyleLight,
                ),
                itemBuilder: (context, item, isDisabled, isSelected) {
                  return Container(
                    padding: const EdgeInsets.all(paddingSmall),
                    child: Text(
                      item,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ).small(color: Colors.black),
                  );
                },
                constraints: const BoxConstraints(maxHeight: 300),
                menuProps: const MenuProps(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(radius),
                    ),
                  ),
                  color: Colors.white,
                  backgroundColor: Colors.white,
                ),
              ),
              onChanged: (newValue) {
                // Find the key corresponding to the selected value
                final selectedKey = field.options?.entries.firstWhere((entry) => entry.value == newValue).key;

                if (selectedKey != null) {
                  controller.updateFilter(key, selectedKey);
                }
              },
              selectedItem: field.options?[value] ?? '',
            ),
          ),
        ],
      ),
    );
  }

  Widget? actionButtons() => null;

  Widget? sectionTitle() => Row(
        children: [
          Text(controller.title).displayLarge(),
          Expanded(
            child: Row(
              children: [
                Container(
                  margin: const EdgeInsets.only(left: paddingMedium),
                  child: IconButton(
                    onPressed: () => controller.loadData(),
                    icon: const Icon(Icons.refresh),
                  ),
                ),
                const Spacer(),
              ],
            ),
          ),
        ],
      );

  /**
   * Builds the main content section of the page.
   *
   * If tabs are defined, it renders a tabbed interface.
   * Otherwise, it renders a data table.
   *
   * @return Widget containing either tabs or a data table
   */
  Widget sectionContent() {
    if (controller.tabs.isNotEmpty) {
      return _contentTab();
    }
    return _contentTable();
  }

  Widget _contentTab() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: primaryColorLight.withAlpha(30),
            borderRadius: BorderRadius.circular(radius),
          ),
          child: TabBar(
            tabs: controller.tabs.map((item) => item.tab).toList(),
            controller: controller.tabController,
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: controller.tabController,
            children: controller.tabs.map((item) => item.content).toList(),
          ),
        ),
      ],
    );
  }

  final scrollController = ScrollController();

  /**
   * Builds the data table content.
   *
   * Creates a PaginatedDataTable with the following features:
   * - Shows checkboxes in selection mode
   * - Provides delete options for selected items
   * - Supports sorting columns
   * - Renders data cells according to field configurations
   * - Handles row selection (for editing or selection)
   *
   * @return A scrollable widget containing the data table
   */
  Widget _contentTable() {
    return SingleChildScrollView(
      child: Obx(
        () => controller.items == null || controller.items!.isEmpty
            ? _buildEmptyState()
            : PaginatedDataTable(
                // dataRowMinHeight: 40, // tinggi minimum (Flutter 3.10+)
                // dataRowMaxHeight: 100, // tinggi maksimum (Flutter 3.10+)
                header: (controller.isSelectionMode.isTrue && controller.selectedItems.isNotEmpty)
                    ? Row(
                        children: [
                          TextButton(
                            onPressed: () => _showDeleteConfirmation(multiple: true),
                            child: Text('Delete ${controller.selectedItems.length}').medium(color: textColorRed),
                          ),
                          const SizedBox(width: paddingSmall),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: controller.clearSelection,
                            tooltip: 'Clear',
                          ),
                        ],
                      )
                    : null,
                rowsPerPage: controller.pageSize,
                availableRowsPerPage: const [10, 25, 50],
                onRowsPerPageChanged: (value) {
                  if (value != null) {
                    controller.pageSize = value;
                    controller.goToFirstPage();
                  }
                },
                showCheckboxColumn: controller.isSelectionMode.value,
                sortColumnIndex: _getSortColumnIndex(),
                sortAscending: controller.sortAscending.value,
                showEmptyRows: false,
                columns: _buildDataColumns(),
                headingRowColor: WidgetStateProperty.all(Colors.transparent),
                initialFirstRowIndex: controller.currentPage.value * controller.pageSize,
                source: _DataSource(
                  fieldConfigs: controller.fieldConfigs,
                  items: controller.items ?? {},
                  filteredKeys: controller.items?.keys.toList() ?? [],
                  onRowSelect:
                      controller.isSelectionMode.value ? controller.toggleItemSelection : controller.clickDetails,
                  selectedItems: controller.selectedItems,
                  columns: controller.columns?.keys.toList() ?? [],
                  isSelectionMode: controller.isSelectionMode.value,
                  totalItems: controller.totalItems.value,
                  currentPage: controller.currentPage.value,
                  pageSize: controller.pageSize,
                ),
                onPageChanged: (pageIndex) {
                  controller.goToPage((pageIndex / controller.pageSize) as int);
                },
                onSelectAll: (selected) {
                  if (selected != null) {
                    selected ? controller.selectAll() : controller.clearSelection();
                  }
                },
              ),
      ),
    );
  }

  List<DataColumn> _buildDataColumns() {
    return (controller.columns ?? {})
        .entries
        .map(
          (entry) => DataColumn(
            label: Expanded(
              child: Text(
                entry.value,
                overflow: TextOverflow.ellipsis,
              ).medium(
                weight: FontWeight.bold,
              ),
            ),
            onSort: (columnIndex, ascending) => controller.sort(entry.key),
          ),
        )
        .toList();
  }

  void _showDeleteConfirmation({bool multiple = false, String? singleItemKey}) {
    Get.dialog(
      AlertDialog(
        title: Text(multiple ? 'Delete Selected Items' : 'Delete Item'),
        content: Text(multiple
            ? 'Are you sure you want to delete ${controller.selectedItems.length} selected items?'
            : 'Are you sure you want to delete this item?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success =
                  multiple ? await controller.deleteSelectedItems() : await controller.deleteData(singleItemKey!);

              toast(
                text: success
                    ? '${multiple ? 'Items' : 'Item'} deleted successfully'
                    : 'Failed to delete ${multiple ? 'items' : 'item'}',
                type: success ? ToastificationType.success : ToastificationType.error,
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  int? _getSortColumnIndex() {
    if (controller.sortColumn.isEmpty) return null;
    final index = controller.columns?.keys.toList().indexOf(controller.sortColumn.value) ?? -1;
    return index >= 0 ? index : null;
  }

  /// Shows a bottom sheet with search and filter options for mobile view
  void _showMobileActionsSheet(BuildContext context) {
    isBottomSheetOpen.value = true;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(radius)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.4,
          maxChildSize: 0.8,
          expand: false,
          builder: (context, scrollController) {
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(radius),
                  topRight: Radius.circular(radius),
                ),
              ),
              padding: const EdgeInsets.all(paddingMedium),
              child: Column(
                spacing: paddingMedium,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with close button
                  Row(
                    children: [
                      const Text('Search & Filters').displayMedium(),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                  const Divider(),

                  // Search section - using the same component as web
                  if (controller.enableSearch)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Search').medium(weight: FontWeight.bold),
                        const SizedBox(height: paddingSmall),
                        SizedBox(
                          height: 50, // Fixed height to prevent layout issues
                          child: sectionLeft(isMobile: true),
                        ),
                      ],
                    ),

                  // Filters section - using the same component as web
                  if (controller.enableFilter && controller.filters.isNotEmpty)
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Filters').medium(weight: FontWeight.bold),
                          const SizedBox(height: paddingSmall),
                          sectionRight(isMobile: true),
                        ],
                      ),
                    ),

                  // Apply button
                  Container(
                    width: double.infinity,
                    margin: const EdgeInsets.only(top: paddingSmall),
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: const Text('Apply'),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    ).then((_) {
      isBottomSheetOpen.value = false;
    });
  }

  /// Builds an empty state widget to display when no data is available
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: paddingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: paddingMedium),
          Text(
            'No data available',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: paddingSmall),
          Text(
            'Try changing your search or filter criteria',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }
}

class _DataSource extends DataTableSource {
  final bool isSelectionMode;
  List<String>? filteredKeys;
  final Map<String, Map> items;
  final List<String> columns;
  final Set<String> selectedItems;
  final Function(String) onRowSelect;
  final int totalItems;
  final int currentPage;
  final int pageSize;
  final List<FieldConfig> fieldConfigs;

  _DataSource({
    required this.items,
    this.filteredKeys,
    required this.onRowSelect,
    required this.selectedItems,
    required this.columns,
    required this.isSelectionMode,
    required this.totalItems,
    required this.currentPage,
    required this.pageSize,
    required this.fieldConfigs,
  }) {
    if (filteredKeys == null || filteredKeys!.isEmpty) {
      filteredKeys = [];
      filteredKeys!.addAll(items.keys.toList());
    }
  }

  @override
  DataRow getRow(int index) {
    // Check if index is within bounds of filteredKeys
    if (filteredKeys == null || index >= filteredKeys!.length) {
      // Return an empty row if index is out of bounds
      return DataRow(
        cells: List.generate(
          columns.length,
          (i) => const DataCell(Text('')),
        ),
      );
    }

    final key = filteredKeys?[index] ?? '';
    final item = items[key];
    final isSelected = selectedItems.contains(key);

    return DataRow(
      selected: isSelected,
      onSelectChanged: (selected) {
        if (selected != null) {
          onRowSelect(key);
        }
      },
      cells: columns.map((column) {
        if (fieldConfigs.isNotEmpty) {
          for (var fieldConfig in fieldConfigs) {
            String k = fieldConfig.key;
            // Preset key dengan logic, if exists
            if (fieldConfig.loadFromKeyLogic != null) {
              k = fieldConfig.loadFromKeyLogic!(
                item?[fieldConfig.loadFromKey] ?? item?[fieldConfig.key],
              );
            } else if (fieldConfig.loadFromKey.isNotEmpty) {
              k = fieldConfig.loadFromKey;
            }

            if (fieldConfig.key != column) continue;
            if (fieldConfig.customColumnWidget == null) continue;

            return DataCell(
              Container(
                constraints: const BoxConstraints(
                  maxWidth: 200,
                ),
                child: fieldConfig.customColumnWidget!(item?[k]),
              ),
            );
          }
        }
        return DataCell(
          Container(
            constraints: const BoxConstraints(
              maxWidth: 350,
            ),
            child: Text(
              (item?[column] ?? '').toString(),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
      }).toList(),
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => totalItems;

  @override
  int get selectedRowCount => selectedItems.length;
}
