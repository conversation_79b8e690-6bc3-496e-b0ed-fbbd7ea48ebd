import 'package:get/get.dart';
import 'package:superapps_cms/routes/app_routes.dart';
import 'package:superapps_cms/routes/home_controller.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/utils.dart';
import 'package:toastification/toastification.dart';

class ApiCommon extends GetConnect {
  bool get debug => true;

  final theme = '/api/cms/theme';
  final themeCopy = '/api/cms/theme/:id/copy';
  final menu = '/api/cms/menu';
  final flyer = '/api/cms/flyer';
  final flyerUpload = '/api/cms/flyer/upload';
  final globalConfig = '/api/cms/globalConfig';
  final information = '/api/cms/information';
  final informationUpload = '/api/cms/information/upload';
  final question = '/api/cms/question';
  final durasiReset = '/api/cms/duration-reset';
  final hospital = '/api/cms/hospital';

  @override
  String? get baseUrl => "https://nrwvafwa4f.execute-api.ap-southeast-3.amazonaws.com/dev/common";
  // String? get baseUrl => "https://sa-agent-pdl-uat.qloudkita.id";

  // Helper method to handle unauthorized responses (401)
  void _handleUnauthorized() {
    try {
      // Log the unauthorized access attempt
      logger.w('Unauthorized access detected. Logging out user.');

      // Show toast message to user
      toast(text: "Session expired. Please login again.", type: ToastificationType.warning);

      // Get HomeController and call clickLogout
      if (Get.isRegistered<HomeController>()) {
        Get.find<HomeController>().clickLogout();
      } else {
        // Fallback if HomeController is not registered
        storage.remove(keyAccessToken);
        storage.remove(keyRefreshToken);
        storage.remove(keyIsLoggedIn);
        Get.offAllNamed(Routes.LOGIN);
      }
    } catch (e) {
      // If anything goes wrong, ensure we at least redirect to login
      logger.e('Error during logout: $e');
      Get.offAllNamed(Routes.LOGIN);
    }
  }

  @override
  void onInit() {
    super.onInit();

    // Configure HTTP client with security settings
    httpClient.timeout = const Duration(seconds: 30); // Set reasonable timeout
    httpClient.maxAuthRetries = 3; // Limit auth retries

    httpClient.addRequestModifier<dynamic>((request) async {
      String accessToken = storage.read(keyAccessToken) ?? '';

      // Only log URL in debug mode, never log sensitive tokens
      if (debug) {
        logger.w("API Request: ${request.method} ${request.url}");
        // SECURITY: Never log access tokens in production
      }

      // Add security headers (only if not already set)
      if (!request.headers.containsKey('Content-Type')) {
        request.headers['Content-Type'] = 'application/json';
      }
      request.headers['Accept'] = 'application/json';
      request.headers['X-Requested-With'] = 'XMLHttpRequest';

      // SECURITY: Remove CORS headers from client-side requests
      // CORS should be handled by the server, not the client
      // These headers are ineffective and potentially misleading when set by client

      if (accessToken.isNotEmpty) {
        request.headers['Authorization'] = 'Bearer $accessToken';
      }

      return request;
    });

    // Add response modifier to handle various HTTP status codes
    httpClient.addResponseModifier((request, response) {
      // Handle unauthorized access
      if (response.statusCode == 401) {
        _handleUnauthorized();
      }

      // Handle forbidden access
      if (response.statusCode == 403) {
        logger.w('Access forbidden for ${request.url}');
        toast(text: "Access denied. Insufficient permissions.", type: ToastificationType.error);
      }

      // Handle server errors
      if (response.statusCode != null && response.statusCode! >= 500) {
        logger.e('Server error ${response.statusCode} for ${request.url}');
        toast(text: "Server error. Please try again later.", type: ToastificationType.error);
      }

      return response;
    });
  }
}
