import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

/**
 * # Input Security Utilities
 * 
 * Comprehensive security utilities for input validation, sanitization,
 * and protection against common security vulnerabilities.
 */
class InputSecurity {
  // Dangerous file extensions that should never be allowed
  static const List<String> _dangerousExtensions = [
    'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'app',
    'deb', 'pkg', 'rpm', 'dmg', 'iso', 'msi', 'sh', 'ps1', 'php', 'asp',
    'aspx', 'jsp', 'py', 'rb', 'pl', 'cgi', 'htaccess', 'htpasswd'
  ];

  // Suspicious patterns that might indicate injection attempts
  static const List<String> _suspiciousPatterns = [
    '<script', '</script>', 'javascript:', 'vbscript:', 'onload=', 'onerror=',
    'onclick=', 'onmouseover=', 'onfocus=', 'onblur=', 'onchange=', 'onsubmit=',
    'eval(', 'setTimeout(', 'setInterval(', 'Function(', 'constructor(',
    'alert(', 'confirm(', 'prompt(', 'document.', 'window.', 'location.',
    'DROP TABLE', 'DELETE FROM', 'INSERT INTO', 'UPDATE SET', 'UNION SELECT',
    'OR 1=1', 'AND 1=1', '--', '/*', '*/', 'xp_', 'sp_', 'exec(',
    '../', '..\\', '/etc/', '/bin/', '/usr/', '/var/', '/tmp/',
    'C:\\', 'D:\\', 'E:\\', 'F:\\', 'G:\\', 'H:\\', 'I:\\', 'J:\\',
  ];

  // Maximum allowed lengths for different input types
  static const Map<String, int> _maxLengths = {
    'name': 100,
    'email': 254, // RFC 5321 limit
    'phone': 20,
    'address': 500,
    'description': 2000,
    'url': 2048,
    'filename': 255,
    'general': 255,
  };

  /// Sanitizes text input by removing/escaping dangerous characters
  static String sanitizeText(String input, {String type = 'general'}) {
    if (input.isEmpty) return input;

    // Trim whitespace
    String sanitized = input.trim();

    // Check maximum length
    final maxLength = _maxLengths[type] ?? _maxLengths['general']!;
    if (sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength);
    }

    // Remove null bytes and control characters (except newlines and tabs for descriptions)
    if (type == 'description') {
      sanitized = sanitized.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');
    } else {
      sanitized = sanitized.replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '');
    }

    // HTML encode dangerous characters
    sanitized = sanitized
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;')
        .replaceAll('/', '&#x2F;');

    return sanitized;
  }

  /// Validates and sanitizes email addresses
  static String? validateEmail(String email) {
    if (email.isEmpty) return null;

    final sanitized = sanitizeText(email, type: 'email');
    
    // Basic email regex (more permissive than RFC 5322 but secure)
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    
    if (!emailRegex.hasMatch(sanitized)) {
      return null;
    }

    // Additional security checks
    if (containsSuspiciousPatterns(sanitized)) {
      return null;
    }

    return sanitized.toLowerCase();
  }

  /// Validates phone numbers
  static String? validatePhone(String phone) {
    if (phone.isEmpty) return null;

    // Remove all non-digit characters except + at the beginning
    String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Ensure + is only at the beginning
    if (cleaned.contains('+') && !cleaned.startsWith('+')) {
      return null;
    }

    // Check length (international format: +1234567890 to +123456789012345)
    if (cleaned.length < 7 || cleaned.length > 16) {
      return null;
    }

    return cleaned;
  }

  /// Validates URLs
  static String? validateUrl(String url) {
    if (url.isEmpty) return null;

    final sanitized = sanitizeText(url, type: 'url');
    
    try {
      final uri = Uri.parse(sanitized);
      
      // Only allow HTTP and HTTPS schemes
      if (!['http', 'https'].contains(uri.scheme.toLowerCase())) {
        return null;
      }

      // Check for suspicious patterns
      if (containsSuspiciousPatterns(sanitized)) {
        return null;
      }

      return sanitized;
    } catch (e) {
      return null;
    }
  }

  /// Checks if input contains suspicious patterns
  static bool containsSuspiciousPatterns(String input) {
    final lowerInput = input.toLowerCase();
    
    for (final pattern in _suspiciousPatterns) {
      if (lowerInput.contains(pattern.toLowerCase())) {
        return true;
      }
    }
    
    return false;
  }

  /// Validates file uploads
  static Map<String, dynamic> validateFile({
    required String filename,
    required int fileSize,
    required List<String> allowedExtensions,
    int maxFileSize = 2 * 1024 * 1024, // 2MB default
    Uint8List? fileBytes,
  }) {
    final result = <String, dynamic>{
      'isValid': false,
      'error': null,
      'sanitizedFilename': null,
    };

    // Sanitize filename
    String sanitizedFilename = sanitizeFilename(filename);
    if (sanitizedFilename.isEmpty) {
      result['error'] = 'Invalid filename';
      return result;
    }

    // Check file extension
    final extension = sanitizedFilename.split('.').last.toLowerCase();
    
    // Check for dangerous extensions
    if (_dangerousExtensions.contains(extension)) {
      result['error'] = 'File type not allowed for security reasons';
      return result;
    }

    // Check against allowed extensions
    if (!allowedExtensions.contains(extension)) {
      result['error'] = 'File type not allowed. Allowed types: ${allowedExtensions.join(', ')}';
      return result;
    }

    // Check file size
    if (fileSize > maxFileSize) {
      result['error'] = 'File size exceeds maximum allowed size';
      return result;
    }

    // Basic file content validation if bytes are provided
    if (fileBytes != null) {
      if (!_validateFileContent(fileBytes, extension)) {
        result['error'] = 'File content does not match file extension';
        return result;
      }
    }

    result['isValid'] = true;
    result['sanitizedFilename'] = sanitizedFilename;
    return result;
  }

  /// Sanitizes filenames
  static String sanitizeFilename(String filename) {
    if (filename.isEmpty) return '';

    // Remove path separators and dangerous characters
    String sanitized = filename
        .replaceAll(RegExp(r'[<>:"/\\|?*\x00-\x1F]'), '')
        .replaceAll('..', '')
        .trim();

    // Ensure filename is not empty and has reasonable length
    if (sanitized.isEmpty || sanitized.length > 255) {
      return '';
    }

    // Ensure filename has an extension
    if (!sanitized.contains('.')) {
      return '';
    }

    return sanitized;
  }

  /// Basic file content validation
  static bool _validateFileContent(Uint8List bytes, String extension) {
    if (bytes.isEmpty) return false;

    // Check file signatures (magic numbers)
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return bytes.length >= 2 && bytes[0] == 0xFF && bytes[1] == 0xD8;
      case 'png':
        return bytes.length >= 8 && 
               bytes[0] == 0x89 && bytes[1] == 0x50 && 
               bytes[2] == 0x4E && bytes[3] == 0x47;
      case 'pdf':
        return bytes.length >= 4 && 
               bytes[0] == 0x25 && bytes[1] == 0x50 && 
               bytes[2] == 0x44 && bytes[3] == 0x46;
      case 'gif':
        return bytes.length >= 6 && 
               bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46;
      default:
        return true; // Allow other types without specific validation
    }
  }

  /// Generates a secure hash for file integrity checking
  static String generateFileHash(Uint8List bytes) {
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Validates numeric input
  static double? validateNumber(String input, {double? min, double? max}) {
    if (input.isEmpty) return null;

    final sanitized = sanitizeText(input, type: 'general');
    final number = double.tryParse(sanitized);
    
    if (number == null) return null;
    
    if (min != null && number < min) return null;
    if (max != null && number > max) return null;
    
    return number;
  }

  /// Rate limiting helper (simple in-memory implementation)
  static final Map<String, List<DateTime>> _rateLimitMap = {};
  
  static bool checkRateLimit(String identifier, {int maxAttempts = 5, Duration window = const Duration(minutes: 15)}) {
    final now = DateTime.now();
    final attempts = _rateLimitMap[identifier] ?? [];
    
    // Remove old attempts outside the window
    attempts.removeWhere((attempt) => now.difference(attempt) > window);
    
    // Check if limit exceeded
    if (attempts.length >= maxAttempts) {
      return false;
    }
    
    // Add current attempt
    attempts.add(now);
    _rateLimitMap[identifier] = attempts;
    
    return true;
  }
}
