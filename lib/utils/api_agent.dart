import 'package:get/get.dart';
import 'package:superapps_cms/routes/home_controller.dart';
import 'package:superapps_cms/utils/auth_security.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/utils.dart';
import 'package:toastification/toastification.dart';

class ApiAgent extends GetConnect {
  bool get debug => true;

  final role = '/api/cms/role';
  final access = '/api/cms/access';
  final agents = '/api/cms/agent';
  final users = '/api/cms/user';
  final approvalLevel = '/api/cms/approval-level';
  final approval = '/api/cms/approval';
  final profile = '/api/cms/profile';
  final profilePicture = '/api/cms/profile/profile-picture';
  final changePass = '/api/cms/profile/change-password';
  final branch = '/api/cms/branch';
  final city = '/api/cms/branch/city';
  final timezone = '/api/cms/user/timezone';
  final branchSync = '/api/cms/branch/sync';
  final batchJob = '/api/cms/monitoring/batch/job';
  final training = '/api/cms/training';
  final signature = '/api/cms/signature';
  final inbox = '/api/inbox';

  @override
  String? get baseUrl => "https://nrwvafwa4f.execute-api.ap-southeast-3.amazonaws.com/dev/agent";
  // String? get baseUrl => "https://sa-common-pdl-uat.qloudkita.id";

  // Helper method to handle unauthorized responses (401)
  void _handleUnauthorized() {
    try {
      // Log the unauthorized access attempt
      logger.w('Unauthorized access detected. Logging out user.');

      // Show toast message to user
      toast(text: "Session expired. Please login again.", type: ToastificationType.warning);

      // Use enhanced secure logout
      AuthSecurity.secureLogout(reason: 'unauthorized_access');

      // Get HomeController and call clickLogout as fallback
      if (Get.isRegistered<HomeController>()) {
        Get.find<HomeController>().clickLogout();
      }
    } catch (e) {
      // If anything goes wrong, ensure we at least redirect to login
      logger.e('Error during logout: $e');
      AuthSecurity.secureLogout(reason: 'logout_error');
    }
  }

  @override
  void onInit() {
    super.onInit();

    // Configure HTTP client with security settings
    httpClient.timeout = const Duration(seconds: 30); // Set reasonable timeout
    httpClient.maxAuthRetries = 3; // Limit auth retries

    httpClient.addRequestModifier<dynamic>((request) async {
      String accessToken = storage.read(keyAccessToken) ?? '';

      // Only log URL in debug mode, never log sensitive tokens
      if (debug) {
        logger.w("API Request: ${request.method} ${request.url}");
        // SECURITY: Never log access tokens in production
      }

      // Skip header modification for login requests (they handle their own headers)
      if (request.url.toString().contains('/auth/login')) {
        if (debug) logger.w("Skipping header modification for login request");
        return request;
      }

      // Add security headers (only if not already set)
      if (!request.headers.containsKey('Content-Type')) {
        request.headers['Content-Type'] = 'application/json';
      }
      request.headers['Accept'] = 'application/json';
      request.headers['X-Requested-With'] = 'XMLHttpRequest';

      // SECURITY: Remove CORS headers from client-side requests
      // CORS should be handled by the server, not the client
      // These headers are ineffective and potentially misleading when set by client

      if (accessToken.isNotEmpty) {
        request.headers['Authorization'] = 'Bearer $accessToken';
      }

      return request;
    });

    // Add response modifier to handle various HTTP status codes
    httpClient.addResponseModifier((request, response) {
      // Handle unauthorized access
      if (response.statusCode == 401) {
        _handleUnauthorized();
      }

      // Handle forbidden access
      if (response.statusCode == 403) {
        logger.w('Access forbidden for ${request.url}');
        toast(text: "Access denied. Insufficient permissions.", type: ToastificationType.error);
      }

      // Handle server errors
      if (response.statusCode != null && response.statusCode! >= 500) {
        logger.e('Server error ${response.statusCode} for ${request.url}');
        toast(text: "Server error. Please try again later.", type: ToastificationType.error);
      }

      return response;
    });
  }

  Future<Response> login({required String username, required String password}) async {
    try {
      if (debug) {
        logger.w("Attempting login with minimal headers");
        logger.w("Base URL: $baseUrl");
        logger.w("Full login URL will be: $baseUrl/api/cms/auth/login");
      }

      // Try the simplest approach first - let GetConnect handle everything
      final response = await httpClient.post(
        '/api/cms/auth/login',
        body: {
          'username': username,
          'password': password,
        },
      );

      if (debug) logger.w("Login response status: ${response.statusCode}");
      return response;
    } catch (e) {
      if (debug) logger.e("Login failed: $e");

      // Fallback: try with explicit form encoding
      try {
        if (debug) logger.w("Trying fallback with form-encoded data");

        final formBody = 'username=${Uri.encodeComponent(username)}&password=${Uri.encodeComponent(password)}';

        final response = await httpClient.post(
          '/api/cms/auth/login',
          body: formBody,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            // Remove X-Requested-With to avoid CORS preflight
          },
        );

        return response;
      } catch (e2) {
        if (debug) {
          logger.e("All login attempts failed: $e2");
          logger.e("Final URL attempted: $baseUrl/api/cms/auth/login");
        }
        rethrow;
      }
    }
  }

  Future<Response> getAccess() => get(access, query: {'size': '1000', 'page': '0'});

  // Profile
  Future<Response> getprofile() => get(profile);
  Future<Response> updateProfile(Map<String, dynamic> data) => post(profile, data);
  Future<Response> updateProfilePicture(dynamic data) => post(profilePicture, data);
  Future<Response> changePassword(Map<String, dynamic> data) => post(changePass, data);
}
