import 'dart:convert';
import 'dart:math';
import 'package:get/get.dart';
import 'package:superapps_cms/routes/app_routes.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/input_security.dart';
import 'package:superapps_cms/utils/utils.dart';

/**
 * # Authentication Security Utilities
 * 
 * Enhanced security utilities for authentication, session management,
 * and authorization with protection against common security vulnerabilities.
 */
class AuthSecurity {
  // Session timeout in minutes
  static const int _sessionTimeoutMinutes = 30;

  // Maximum login attempts before lockout
  static const int _maxLoginAttempts = 5;

  // Lockout duration in minutes
  static const int _lockoutDurationMinutes = 15;

  // Token refresh threshold (refresh when token expires in X minutes)
  static const int _tokenRefreshThresholdMinutes = 5;

  /// Validates login credentials with enhanced security
  static Map<String, dynamic> validateLoginCredentials(String email, String password) {
    final result = <String, dynamic>{
      'isValid': false,
      'errors': <String>[],
      'sanitizedEmail': null,
    };

    // Validate and sanitize email
    final sanitizedEmail = InputSecurity.validateEmail(email);
    if (sanitizedEmail == null) {
      result['errors'].add('Please enter a valid email address');
    } else {
      result['sanitizedEmail'] = sanitizedEmail;
    }

    // Validate password
    if (password.isEmpty) {
      result['errors'].add('Password is required');
    } else if (password.length < 8) {
      result['errors'].add('Password must be at least 8 characters long');
    } else if (InputSecurity.containsSuspiciousPatterns(password)) {
      result['errors'].add('Password contains invalid characters');
    }

    // Check rate limiting
    if (sanitizedEmail != null) {
      if (!InputSecurity.checkRateLimit(
        'login_$sanitizedEmail',
        maxAttempts: _maxLoginAttempts,
        window: const Duration(minutes: _lockoutDurationMinutes),
      )) {
        result['errors'].add('Too many login attempts. Please try again later.');
      }
    }

    result['isValid'] = (result['errors'] as List).isEmpty;
    return result;
  }

  /// Securely stores authentication tokens
  static Future<void> storeTokens({
    required String accessToken,
    required String refreshToken,
  }) async {
    // Validate tokens before storing
    if (!_isValidJWT(accessToken) || !_isValidJWT(refreshToken)) {
      throw Exception('Invalid token format');
    }

    // Store tokens with timestamp for expiration tracking
    final tokenData = {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    await storage.write(keyAccessToken, accessToken);
    await storage.write(keyRefreshToken, refreshToken);
    await storage.write('tokenData', tokenData);
    await storage.write(keyIsLoggedIn, true);
  }

  /// Checks if the current session is valid
  static bool isSessionValid() {
    final isLoggedIn = storage.read(keyIsLoggedIn) ?? false;
    if (!isLoggedIn) return false;

    final tokenData = storage.read('tokenData');
    if (tokenData == null) return false;

    final timestamp = tokenData['timestamp'] as int?;
    if (timestamp == null) return false;

    final sessionAge = DateTime.now().millisecondsSinceEpoch - timestamp;
    final sessionAgeMinutes = sessionAge / (1000 * 60);

    return sessionAgeMinutes < _sessionTimeoutMinutes;
  }

  /// Checks if token needs refresh
  static bool shouldRefreshToken() {
    final accessToken = storage.read(keyAccessToken);
    if (accessToken == null) return false;

    try {
      final payload = _decodeJWTPayload(accessToken);
      final exp = payload['exp'] as int?;
      if (exp == null) return true;

      final expirationTime = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      final timeUntilExpiration = expirationTime.difference(DateTime.now());

      return timeUntilExpiration.inMinutes <= _tokenRefreshThresholdMinutes;
    } catch (e) {
      logger.e('Error checking token expiration: $e');
      return true;
    }
  }

  /// Securely clears all authentication data
  static Future<void> clearAuthData() async {
    await Future.wait([
      storage.remove(keyAccessToken),
      storage.remove(keyRefreshToken),
      storage.remove(keyIsLoggedIn),
      storage.remove('tokenData'),
      storage.remove('userProfile'),
      storage.remove('userPermissions'),
    ]);
  }

  /// Validates JWT token format
  static bool _isValidJWT(String token) {
    if (token.isEmpty) return false;

    final parts = token.split('.');
    if (parts.length != 3) return false;

    try {
      // Try to decode each part to ensure it's valid base64
      for (final part in parts) {
        base64Url.decode(base64Url.normalize(part));
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Decodes JWT payload (without verification - for client-side info only)
  static Map<String, dynamic> _decodeJWTPayload(String token) {
    final parts = token.split('.');
    if (parts.length != 3) throw Exception('Invalid JWT format');

    final payload = parts[1];
    final normalized = base64Url.normalize(payload);
    final decoded = utf8.decode(base64Url.decode(normalized));

    return json.decode(decoded) as Map<String, dynamic>;
  }

  /// Generates a secure session ID
  static String generateSessionId() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }

  /// Validates user permissions for a specific action
  static bool hasPermission(String domain, {List<String>? requiredRoles}) {
    try {
      final userProfile = storage.read('userProfile');
      if (userProfile == null) return false;

      // Check if user has required roles
      if (requiredRoles != null && requiredRoles.isNotEmpty) {
        final userRoles = userProfile['roles'] as List<dynamic>? ?? [];
        final userRoleStrings = userRoles.map((r) => r.toString()).toList();

        // User must have at least one of the required roles
        final hasRequiredRole = requiredRoles.any((role) => userRoleStrings.contains(role));
        if (!hasRequiredRole) return false;
      }

      // Check domain-specific permissions
      final permissions = userProfile['permissions'] as List<dynamic>? ?? [];
      final hasPermission = permissions.any((p) => p.toString().contains(domain));

      return hasPermission;
    } catch (e) {
      logger.e('Error checking permissions: $e');
      return false;
    }
  }

  /// Logs security events for monitoring
  static void logSecurityEvent(String event, {Map<String, dynamic>? details}) {
    final logEntry = {
      'timestamp': DateTime.now().toIso8601String(),
      'event': event,
      'details': details ?? {},
      'sessionId': generateSessionId(),
    };

    logger.w('Security Event: ${json.encode(logEntry)}');
  }

  /// Validates session and redirects if invalid
  static bool validateSessionAndRedirect() {
    if (!isSessionValid()) {
      logSecurityEvent('session_expired');
      clearAuthData();
      Get.offAllNamed(Routes.LOGIN);
      return false;
    }
    return true;
  }

  /// Enhanced logout with security cleanup
  static Future<void> secureLogout({String reason = 'user_initiated'}) async {
    logSecurityEvent('logout', details: {'reason': reason});

    await clearAuthData();

    // Clear any cached data that might contain sensitive information
    Get.deleteAll(force: true);

    // Navigate to login
    Get.offAllNamed(Routes.LOGIN);
  }

  /// Checks for suspicious activity patterns
  static bool detectSuspiciousActivity() {
    try {
      // Check for rapid successive requests
      final lastRequestTime = storage.read('lastRequestTime') as int?;
      final currentTime = DateTime.now().millisecondsSinceEpoch;

      if (lastRequestTime != null) {
        final timeDiff = currentTime - lastRequestTime;
        if (timeDiff < 100) {
          // Less than 100ms between requests
          logSecurityEvent('suspicious_rapid_requests');
          return true;
        }
      }

      storage.write('lastRequestTime', currentTime);
      return false;
    } catch (e) {
      logger.e('Error detecting suspicious activity: $e');
      return false;
    }
  }

  /// Validates and sanitizes user profile data
  static Map<String, dynamic> sanitizeUserProfile(Map<String, dynamic> profile) {
    final sanitized = <String, dynamic>{};

    for (final entry in profile.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is String) {
        // Sanitize string values
        sanitized[key] = InputSecurity.sanitizeText(value);
      } else if (value is List) {
        // Sanitize list items if they're strings
        sanitized[key] = value.map((item) {
          if (item is String) {
            return InputSecurity.sanitizeText(item);
          }
          return item;
        }).toList();
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }
}
