import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:superapps_cms/components/chip.dart';
import 'package:superapps_cms/controllers/profile_component_controller.dart';
import 'package:superapps_cms/routes/home_controller.dart';
import 'package:superapps_cms/routes/navigation_config.dart';
import 'package:superapps_cms/utils/api_agent.dart';
import 'package:superapps_cms/utils/api_common.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/extension.dart';
import 'package:toastification/toastification.dart';

// Responsive
Widget responsiveWidget({required Widget phone, Widget? tablet, required Widget web}) {
  if (isBigScreen()) {
    return web;
  } else if (isMediumScreen()) {
    return tablet ?? phone;
  } else {
    return phone;
  }
}

bool isBigScreen() {
  var deviceWidth = Get.context!.mediaQuerySize.shortestSide;
  if (GetPlatform.isDesktop) {
    deviceWidth = Get.context!.mediaQuerySize.width;
  }

  return deviceWidth >= 1200;
}

bool isMediumScreen() {
  var deviceWidth = Get.context!.mediaQuerySize.shortestSide;
  if (GetPlatform.isDesktop) {
    deviceWidth = Get.context!.mediaQuerySize.width;
  }

  return deviceWidth >= 600;
}

bool isSmallScreen() {
  var deviceWidth = Get.context!.mediaQuerySize.shortestSide;
  if (GetPlatform.isDesktop) {
    deviceWidth = Get.context!.mediaQuerySize.width;
  }

  return deviceWidth < 600;
}

// Toast
toast({required String text, ToastificationType type = ToastificationType.info}) {
  toastification.show(
    title: Text(text),
    autoCloseDuration: const Duration(seconds: 5),
    style: ToastificationStyle.minimal,
    type: type,
  );
}

// Utils
String getRandomString({int length = 8}) {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  final rand = Random();
  return List.generate(length, (_) => chars[rand.nextInt(chars.length)]).join('');
}

Logger get logger => Logger();

ApiAgent get apiAgent {
  if (Get.isRegistered<ApiAgent>()) {
    return Get.find<ApiAgent>();
  } else {
    return Get.put(ApiAgent());
  }
}

ApiCommon get apiCommon {
  if (Get.isRegistered<ApiCommon>()) {
    return Get.find<ApiCommon>();
  } else {
    return Get.put(ApiCommon());
  }
}

GetStorage get storage => GetStorage();

bool get hasHistoryPage => Get.previousRoute.isEmpty;

goToPage(String route) {
  final navItem = NavigationConfig.findById(route);
  if (navItem == null) {
    return;
  }
  if (Get.isRegistered<HomeController>()) {
    Get.find<HomeController>().setActiveItem(navItem);
  } else {
    final controller = Get.put(HomeController());
    controller.setActiveItem(navItem);
  }
}

Widget columnDate(value, {format = dateHumanShort}) {
  if (value is String) {
    return Text(value.toFormattedDate(outputFormat: format) ?? '').medium();
  }
  return const Text('-');
}

Widget columnActive(value) {
  if (value is bool) {
    if (value) {
      return const CustomChip(type: ChipType.success, text: 'Active');
    }
  } else if (value is String) {
    if (value.toLowerCase() == "active") {
      return const CustomChip(type: ChipType.success, text: 'Active');
    }
  }
  return const CustomChip(type: ChipType.warning, text: 'Inactive');
}

Widget columnYesNo(value) {
  if (value is bool) {
    if (value) {
      return const CustomChip(type: ChipType.success, text: 'Yes');
    }
  } else if (value is String) {
    if (value.toLowerCase() == "false") {
      return const CustomChip(type: ChipType.success, text: 'Yes');
    }
  }
  return const CustomChip(type: ChipType.warning, text: 'No');
}

Widget columnImage(value, {imageTag = ''}) {
  if (value == null || value == '') {
    return Container();
  }
  const height = 50.0;

  String randomTag = getRandomString();
  // For mobile, use CachedNetworkImage normally
  return CachedNetworkImage(
    // Use a key that changes when the image changes to force rebuild
    key: ValueKey(randomTag),
    imageUrl: value + "?tag$randomTag",
    height: height,
    width: height * getImageRatio(tag: imageTag),
    fit: BoxFit.cover,
    placeholder: (context, url) => Container(
      height: height,
      width: height * getImageRatio(tag: imageTag),
      color: Colors.grey.shade200,
      child: const Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    ),
  );
}

Widget columnTextFromOptions({value, options}) {
  if (value == null || value == '') {
    return const Text("-").medium();
  }

  if (options == null) {
    return Text(value).medium();
  }

  return Text(options[value] ?? "-").medium();
}

Widget columnBranch(value) {
  if (value == null || value is! List || value.isEmpty) {
    return const Text("-").small(color: textColorPrimary);
  }

  String text = value.map((item) => item['branchName']).join(', ');

  return Text(text).small(color: textColorPrimary);
}

Widget columnRoles(value) {
  if (value == null || value is! List || value.isEmpty) {
    return const Text("-").small(color: textColorPrimary);
  }

  return Wrap(
    clipBehavior: Clip.antiAliasWithSaveLayer,
    children: [
      ...value.map((e) => CustomChip(type: ChipType.info, text: e['name'])),
    ],
  );
}

Widget columnRolesCode(map, value) {
  if (value == null || value.isEmpty || map[value] == null) {
    return const Text("-").small(color: textColorPrimary);
  }

  return CustomChip(type: ChipType.info, text: map[value]);
}

double getImageRatio({String tag = ''}) {
  switch (tag) {
    case tagImageFlyer:
      return imageRatioFlyer;
    default:
      return 1;
  }
}

confirm({
  required String title,
  required String message,
  String confirmText = 'Yes',
  String cancelText = 'No',
}) {
  return Get.dialog(
    AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Get.back(result: false),
          child: Text(cancelText),
        ),
        TextButton(
          onPressed: () => Get.back(result: true),
          child: Text(confirmText),
        ),
      ],
    ),
    barrierDismissible: false,
  );
}

// Approval status colors
Color getApprovalStatusColor(String status, {bool isReviewer = false}) {
  switch (status) {
    case STATUS_BARU:
      return chipColorInfo;
    case STATUS_TERTUNDA:
      return chipColorWarning;
    case STATUS_MENUNGGU_PERSETUJUAN:
      // Different colors for reviewer and approver
      return isReviewer ? Colors.green : chipColorInfo;
    case STATUS_DISETUJUI:
      return chipColorSuccess;
    case STATUS_DITOLAK:
      return chipColorDanger;
    case STATUS_DIBATALKAN:
      return Colors.grey;
    default:
      return chipColorInfo;
  }
}

// Get chip type based on approval status and role
ChipType getApprovalChipType(String status, {bool isReviewer = false}) {
  switch (status) {
    case STATUS_BARU:
      return ChipType.info;
    case STATUS_TERTUNDA:
      return ChipType.warning;
    case STATUS_MENUNGGU_PERSETUJUAN:
      // Different chip types for reviewer and approver
      return isReviewer ? ChipType.success : ChipType.info;
    case STATUS_DISETUJUI:
      return ChipType.success;
    case STATUS_DITOLAK:
      return ChipType.danger;
    case STATUS_DIBATALKAN:
      return ChipType.warning;
    default:
      return ChipType.info;
  }
}

// Image popup utility
void showImagePopup(String imageUrl, {String title = 'Image'}) {
  Get.dialog(
    Dialog(
      child: Container(
        width: 500,
        height: 500,
        padding: const EdgeInsets.all(paddingMedium),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(title).medium(weight: FontWeight.bold),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            Expanded(
              child: Center(
                child: imageUrl.isEmpty || imageUrl == '-'
                    ? Container(
                        height: 500,
                        width: 500,
                        color: Colors.grey.shade200,
                        child: const Center(
                          child: Icon(Icons.person, size: 100, color: Colors.grey),
                        ),
                      )
                    : CachedNetworkImage(
                        imageUrl: imageUrl,
                        height: 500,
                        width: 500,
                        fit: BoxFit.contain,
                        placeholder: (context, url) => const Center(child: CircularProgressIndicator()),
                        errorWidget: (context, url, error) =>
                            const Center(child: Icon(Icons.person, size: 100, color: Colors.grey)),
                      ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

// Controllers
ProfileComponentController getProfileController() {
  if (Get.isRegistered<ProfileComponentController>(tag: "profile")) {
    return Get.find<ProfileComponentController>(tag: "profile");
  }

  return Get.put(ProfileComponentController(), tag: "profile");
}

List<String> get userAccess {
  final profileController = getProfileController();
  return profileController.userAccess;
}

String get userChannel {
  final profileController = getProfileController();
  return profileController.userChannel;
}
