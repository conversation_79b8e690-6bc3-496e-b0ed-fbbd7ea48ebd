import 'package:get/get.dart';
import 'package:superapps_cms/components/base_popup.dart';
import 'package:superapps_cms/components/inbox/inbox_controller.dart';
import 'package:superapps_cms/routes/app_routes.dart';
import 'package:superapps_cms/utils/auth_security.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/utils.dart';

import '../modules/profile/profile_page.dart';
import 'navigation_config.dart';

class HomeController extends GetxController {
  final Rx<NavigationItem> activeItem = NavigationConfig.items.first.obs;
  late final InboxController inboxController;

  @override
  void onInit() {
    super.onInit();

    // Initialize inbox controller
    inboxController = Get.put(InboxController());

    activateByRoute();

    // Buat pemantau manual untuk perubahan rute
    Get.rootDelegate.addListener(() {
      activateByRoute();
    });
  }

  void setActiveItem(NavigationItem item) {
    if (Get.currentRoute != '/${item.id}') {
      Get.toNamed('/${item.id}', preventDuplicates: true);
    }
    activeItem.value = item;
  }

  void activateByRoute() {
    final currentRoute = Get.currentRoute;
    final initialItem = NavigationConfig.getInitialItem(currentRoute);
    activeItem.value = initialItem;
    update(['page']);
  }

  Future<void> clickLogout() async {
    // Use enhanced secure logout
    await AuthSecurity.secureLogout(reason: 'user_initiated');

    // Mark ProfileComponentController for update instead of trying to delete it
    final profileController = getProfileController();
    profileController.onInit(); // Force reinitialize

    // No need to delete other controllers as they'll be recreated when needed
  }

  void clickProfile() {
    // Get.toNamed(Routes.PROFILE);
    Get.dialog(BasePopup(child: Profile()));
  }

  void clickInbox() {
    inboxController.toggleDrawer();
  }

  /// Group Navigation
  // Store which groups are expanded
  final _expandedGroups = <String>{}.obs;

// Check if a group is expanded
  bool isGroupExpanded(String groupName) {
    return _expandedGroups.contains(groupName);
  }

// Toggle group expansion
  void toggleGroupExpansion(String groupName) {
    if (_expandedGroups.contains(groupName)) {
      _expandedGroups.remove(groupName);
    } else {
      _expandedGroups.add(groupName);
    }
    update(['navigation_group_$groupName', 'navigation']);
  }
}
