import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:superapps_cms/routes/app_routes.dart';
import 'package:superapps_cms/utils/auth_security.dart';
import 'package:superapps_cms/utils/base_controller.dart';
import 'package:superapps_cms/utils/web_config.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/encryption_service.dart';
import 'package:superapps_cms/utils/utils.dart';
import 'package:toastification/toastification.dart';

class LoginController extends BaseController {
  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController(text: '');
  final passwordController = TextEditingController(text: '');

  // Track if form fields have content
  final RxBool hasEmailContent = true.obs; // Initialize with true since default text is provided
  final RxBool hasPasswordContent = true.obs; // Initialize with true since default text is provided

  // Computed property to check if form is valid
  bool get isFormValid => hasEmailContent.value && hasPasswordContent.value;

  LoginController() {
    // Add listeners to text controllers to update the content flags
    emailController.addListener(() {
      hasEmailContent.value = emailController.text.isNotEmpty;
    });

    passwordController.addListener(() {
      hasPasswordContent.value = passwordController.text.isNotEmpty;
    });
  }

  /*
    <EMAIL>
    <EMAIL>
    <EMAIL>
    <EMAIL> - Password123!@
   */

  Future<void> login() async {
    if (!formKey.currentState!.validate()) {
      return;
    }
    isLoading.value = true;

    final email = emailController.text;
    final password = passwordController.text;

    // Enhanced security validation
    final validationResult = AuthSecurity.validateLoginCredentials(email, password);
    if (!validationResult['isValid']) {
      final errors = validationResult['errors'] as List<String>;
      toast(text: errors.first, type: ToastificationType.error);
      isLoading.value = false;
      return;
    }

    final sanitizedEmail = validationResult['sanitizedEmail'] as String;
    final secretKey = WebConfig.privateKey;

    final encUsername = Encryption(secretKey).doencrypt(sanitizedEmail, secretKey);
    final encPass = Encryption(secretKey).doencrypt(password, secretKey);

    try {
      final result = await apiAgent.login(username: encUsername, password: encPass);
      if (result.isOk) {
        // Securely store tokens using enhanced security
        await AuthSecurity.storeTokens(
          accessToken: result.body['accessToken'],
          refreshToken: result.body['refreshToken'],
        );

        // Log successful login
        AuthSecurity.logSecurityEvent('login_success', details: {
          'email': sanitizedEmail,
          'timestamp': DateTime.now().toIso8601String(),
        });

        Get.offAllNamed(Routes.HOME);
        toast(text: "Login successful", type: ToastificationType.success);
      } else {
        // Log failed login attempt
        AuthSecurity.logSecurityEvent('login_failed', details: {
          'email': sanitizedEmail,
          'error': result.body['error_description'] ?? 'Unknown error',
        });

        logger.e(result.body);
        toast(text: result.body['error_description'] ?? 'Login failed', type: ToastificationType.error);
      }
    } catch (e) {
      logger.e('Login error: $e');
      toast(text: 'Network error. Please try again.', type: ToastificationType.error);
    }

    isLoading.value = false;
  }
}
