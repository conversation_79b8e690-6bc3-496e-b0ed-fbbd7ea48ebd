import 'package:filter_list/filter_list.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:superapps_cms/models/base_model.dart';
import 'package:superapps_cms/models/tab_content.dart';
import 'package:superapps_cms/pages/item_edit_page.dart';
import 'package:superapps_cms/routes/app_routes.dart';
import 'package:superapps_cms/routes/home_controller.dart';
import 'package:superapps_cms/utils/base_controller.dart';
import 'package:superapps_cms/utils/constants.dart';
import 'package:superapps_cms/utils/field_config.dart';
import 'package:superapps_cms/utils/input_security.dart';
import 'package:superapps_cms/utils/utils.dart';
import 'package:toastification/toastification.dart';

/**
 * # Base CMS Controller (`BaseCMSController` class)
 *
 * ## Overview
 * `BaseCMSController` handles API communication, data loading, filtering, searching,
 * sorting, and CRUD operations for the CMS.
 *
 * ## Key Features
 *
 * - **API Integration**: Handles endpoint configuration and data fetching
 * - **Pagination**: Manages page navigation and limits
 * - **Filtering**: Field-based filtering with dropdown selection
 * - **Sorting**: Column-based sorting with ascending/descending toggle
 * - **Searching**: Global or field-specific search
 * - **CRUD Operations**: Create, read, update, delete functionality
 * - **Form Validation**: Validates user input based on field configurations
 *
 * ## Key Methods
 *
 * - `loadData()`: Fetches data from API with pagination, filtering and sorting
 * - `search()`: Applies search criteria to data fetch
 * - `updateFilter()`: Updates filters and refreshes data
 * - `clickDetails()`: Opens item detail/edit view
 * - `addData()`: Creates new item
 * - `updateData()`: Updates existing item
 * - `deleteData()`: Deletes item
 *
 * ## Example Implementation
 *
 * ```dart
 * class HospitalController extends BaseCMSController {
 *   @override
 *   String get endpoint => apiCommon.hospital;
 *
 *   @override
 *   bool get debug => true;
 *
 *   @override
 *   GetConnect get apiClass => apiCommon;
 *
 *   @override
 *   BaseModel get model => HospitalModel.empty();
 *
 *   // Optional overrides for custom behavior
 *   @override
 *   bool get enableCreate => true;
 *
 *   @override
 *   bool get enableEdit => true;
 *
 *   @override
 *   bool get enableDelete => true;
 * }
 * ```
 *
 * ## Important Properties to Override
 *
 * - `endpoint`: API endpoint path
 * - `apiClass`: API connection instance (apiAgent or apiCommon)
 * - `model`: Model instance for the module
 * - `enableCreate/enableEdit/enableDelete`: Feature flags
 */
abstract class BaseCMSController extends BaseController with GetSingleTickerProviderStateMixin {
  BaseCMSController() {
    // Initialize selectedFilters
    selectedFilters = RxMap<String, String>.from(defaultFilters);
  }

  String get title => model.runtimeType.toString().replaceAll('Model', '');

  // Debug mode
  bool get debug => false;

  bool get showErrorOnToast => false;
  String get showErrorOnToastFromKey => "error_description";

  bool get enableCreate => false;

  // Config for field editing capabilities
  bool get enableEdit => true; // Override to false if editing is not allowed

  // List of fields to display in detail/edit view
  List<FieldConfig> get fieldConfigs => model.getFieldConfigs();
  Map<String, String>? get columns => model.getColumns();

  // Filters
  bool get enableFilter => true;
  Map<String, FieldConfig> get filters => Map.fromEntries(
        model.getFieldConfigs().where((field) => field.enableFilter == true).map(
              (field) => MapEntry(field.key, field),
            ),
      );
  late RxMap<String, String> selectedFilters;
  Map<String, String> get defaultFilters => {};

  // Search and pagination variables
  bool get enableSearch => true;
  RxInt currentPage = RxInt(0);
  int pageSize = 10;
  late RxString searchKeySelection = RxString(searchKey);
  String get searchKey => "q";
  // If searchFields is not empty, it will be used for search instead of searchKey
  Map<String, FieldConfig> get searchFields => Map.fromEntries(
        model.getFieldConfigs().where((field) => field.enableSearch == true).map(
              (field) => MapEntry(field.key, field),
            ),
      );

  // Sorting variables
  final TextEditingController searchController = TextEditingController();
  final RxString sortColumn = RxString('');
  final RxBool sortAscending = RxBool(true);

  // Endpoints
  final RxMap<String, Map<String, dynamic>> _items = RxMap<String, Map<String, dynamic>>();
  RxMap<String, Map<String, dynamic>>? get items => _items;
  GetConnect get apiClass => apiAgent;
  String get endpoint;
  String endpointDetails(key) => '$endpoint/$key';
  String endpointCreate() => endpoint;
  String endpointDelete(key) => '$endpoint/$key';
  String endpointUpdate(key) => '$endpoint/$key';

  // Pagination properties
  final RxInt totalItems = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxBool hasNextPage = false.obs;
  final RxBool hasPreviousPage = false.obs;

  // Delete capabilities
  final RxSet<String> selectedItems = RxSet<String>();
  bool get enableDelete => false;
  RxBool isSelectionMode = false.obs;

  // New abstract method for parsing model
  dynamic parseModel(Map<String, dynamic> json) => model.fromJson(json);

  // Tabs
  late TabController tabController;
  final RxInt currentTabIndex = 0.obs;
  List<TabContent> get tabs => [];
  int get tabInitialIndex => 0;

  // Model
  BaseModel get model;

  @override
  void onInit() {
    super.onInit();

    // Initialize searchKeySelection with first search field if available
    if (searchFields.isNotEmpty) {
      searchKeySelection.value = searchFields.values.first.key;
    }

    loadData();

    // if (tabs.isNotEmpty) {
    //   initTabs();
    // }
  }

  /**
   * Loads data from the API with pagination, sorting, filtering, and search.
   *
   * This method constructs a query with:
   * - Pagination parameters (page size and current page)
   * - Sorting parameters (column and direction)
   * - Search parameters (if a search query is provided)
   * - Filter parameters (from selectedFilters)
   *
   * It then makes an API request and updates the items collection.
   *
   * @return Future containing the API response
   */
  Future? loadData() async {
    isLoading.value = true;

    final query = {
      'size': pageSize.toString(),
      'page': currentPage.value.toString(),
      'sort': sortColumn.value.isNotEmpty ? '${sortColumn.value},${sortAscending.value ? "ASC" : "DESC"}' : '',
    };
    String searchQuery = searchController.text.toString();
    if (searchQuery.isNotEmpty) {
      query[searchKeySelection.value] = searchQuery;
    }

    if (debug) logger.d('Url : $endpoint');
    if (debug) logger.d('Query : $query');

    if (selectedFilters.isNotEmpty) {
      for (var key in selectedFilters.keys) {
        final value = selectedFilters[key] ?? '';
        if (value.isNotEmpty) {
          query[key] = value;
        }
      }
    }

    final response = await apiClass.get(
      endpoint,
      query: query,
    );

    if (response.isOk) {
      if (debug) logger.d(response.body);

      // Clear items saat loading halaman baru
      if (currentPage.value == 0) _items.clear();

      List<dynamic> data = [];
      try {
        data = response.body?['content'] ?? [];
      } catch (e) {
        data = response.body;
      }

      // Update pagination info from response
      try {
        totalItems.value = response.body?['totalElements'] ?? 0;
        totalPages.value = response.body?['totalPages'] ?? 0;
        hasNextPage.value = response.body?['last'] == false;
        hasPreviousPage.value = response.body?['first'] == false;
      } catch (e) {
        totalItems.value = data.length;
        totalPages.value = 0;
        hasNextPage.value = false;
        hasPreviousPage.value = false;
      }

      for (var item in data) {
        final model = parseModel(item);
        final id = model.id;
        _items['$id'] = model.toJson();
      }
    } else {
      logger.e('Failed to load: ${response.body}');

      // Check if the response is 401 Unauthorized
      if (response.statusCode == 401) {
        _handleUnauthorized();
        return response;
      }

      if (showErrorOnToast) {
        showError(response);
      } else {
        toast(text: "Failed to load", type: ToastificationType.error);
      }
    }

    isLoading.value = false;
    return response;
  }

  /**
   * Updates a filter value and refreshes the data.
   *
   * This method updates the selectedFilters map with the provided key-value pair.
   * If the value is null or empty, it removes the filter.
   * After updating the filter, it resets to the first page and reloads data.
   *
   * @param key The filter key to update
   * @param value The new filter value (null or empty to remove)
   */
  void updateFilter(String key, dynamic value) {
    if (value == null || (value is String && value.isEmpty)) {
      // Remove the filter if value is null or empty string
      selectedFilters.remove(key);
    } else {
      // Update the filter with the new value
      selectedFilters[key] = value;
    }

    // Reset to first page when filters change
    currentPage.value = 0;

    // Reload data with the updated filters
    loadData();
  }

  /**
   * Displays a filter dialog for the user to select filter values.
   *
   * This method shows a dialog with the available options for the specified filter key.
   * When the user makes a selection, it updates the selectedFilters map and reloads the data.
   *
   * @param key The filter key to display options for
   */
  void toggleFilter(String key) async {
    final listData = filters[key]?.options?.keys.toList() ?? [];
    final options = filters[key]?.options ?? {};
    final selected = selectedFilters[key] ?? '';
    List<String> selectedList = selected.isEmpty ? [] : [selected];
    final result = await showFilterDialog<String>(
      Get.context!,
      listData: listData,
      selectedListData: selectedList,
      title: 'Select ${filters[key]?.label}',
      labelBuilder: (type) {
        return options[type] ?? '';
      },
      validateSelectedItem: (list, val) => list!.contains(val),
      searchFunction: (type, query) {
        return type.toLowerCase().contains(query.toLowerCase());
      },
    );
    if (result.isEmpty) {
      selectedFilters.remove(key);
    } else {
      selectedFilters[key] = result.isEmpty ? '' : result.first ?? '';
    }
    loadData();
  }

  Future<List<dynamic>> showFilterDialog<T extends Object>(
    BuildContext context, {
    required List<T> listData,
    required List<T> selectedListData,
    required String Function(T?) labelBuilder,
    required bool Function(List<T>?, T) validateSelectedItem,
    required bool Function(T, String) searchFunction,
    String title = 'Select Items',
  }) async {
    List<T> result = [];

    await FilterListDialog.display<T>(
      context,
      listData: listData,
      selectedListData: selectedListData,
      choiceChipLabel: labelBuilder,
      validateSelectedItem: validateSelectedItem,
      onItemSearch: searchFunction,
      headlineText: title,
      applyButtonText: 'Apply',
      resetButtonText: 'Reset',
      allButtonText: 'All',
      selectedItemsText: 'Selected Items',
      // closeIconColor: Colors.grey,
      themeData: FilterListThemeData(
        context,
        choiceChipTheme: ChoiceChipThemeData(
          backgroundColor: Colors.grey[200],
          textStyle: bodyStyleLight,
          margin: const EdgeInsets.all(paddingExtraSmall),
          selectedTextStyle: bodyStyleLight,
          selectedBackgroundColor: primaryColorLight.withAlpha(20),
          selectedShadowColor: Colors.transparent,
          elevation: 0,
        ),
        headerTheme: HeaderThemeData(
          headerTextStyle: bodyStyleLight.copyWith(fontWeight: FontWeight.bold),
        ),
      ),
      onApplyButtonClick: (list) {
        result = list ?? [];
        Navigator.pop(context);
      },
      maximumSelectionLength: 1,
      hideSelectedTextCount: true,
      hideSearchField: true,
      width: 300,
      insetPadding: const EdgeInsets.all(paddingMedium),
    );

    return result;
  }

  /**
   * Sorts the data by the specified column.
   *
   * If the column is already the sort column, it toggles between ascending and descending.
   * Otherwise, it sets the new sort column and defaults to ascending order.
   * After updating the sort parameters, it resets to the first page and reloads data.
   *
   * @param column The column name to sort by
   */
  void sort(String column) {
    if (sortColumn.value == column) {
      sortAscending.value = !sortAscending.value;
    } else {
      sortColumn.value = column;
      sortAscending.value = true;
    }
    currentPage.value = 0;
    loadData(); // Reload data from API with new sort parameters
  }

  /**
   * Initiates a search with the current search query.
   *
   * Resets to the first page and reloads data with the search parameters
   * from the searchController.
   */
  void search() {
    currentPage.value = 0;
    loadData();
  }

  /**
   * Pagination methods
   */

  /**
   * Navigates to a specific page in the paginated dataset.
   *
   * Checks if the requested page is valid (within bounds) and
   * if so, updates currentPage and reloads data.
   *
   * @param page The page number to navigate to (1-based)
   */
  void goToPage(int page) {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
      loadData();
    }
  }

  /**
   * Navigates to the first page in the paginated dataset.
   *
   * Sets currentPage to 0 and reloads data.
   */
  void goToFirstPage() {
    currentPage.value = 0;
    loadData();
  }

  /**
   * Reloads the current page.
   *
   * Useful for refreshing data after operations like delete.
   */
  void goToLastPage() {
    loadData();
  }

  /**
   * Handles clicking on a table row to view or edit an item.
   *
   * Opens an ItemEditPage dialog with the selected item.
   * The dialog will be in edit or view-only mode based on the enableEdit property.
   *
   * @param key The unique identifier of the item to view/edit
   */
  void clickDetails(String key) {
    final item = items?[key];
    if (item != null) {
      if (enableEdit) {
        // Navigate to detail/edit page using dialog
        Get.dialog(
          ItemEditPage(
            parentController: this,
            itemKey: key,
          ),
          barrierDismissible: false,
        );
      } else {
        // View-only detail page or dialog
        Get.dialog(
          ItemEditPage(
            parentController: this,
            itemKey: key,
          ),
          barrierDismissible: false,
        );
      }
    }
  }

  /**
   * Updates an existing item with the provided data.
   *
   * This method:
   * 1. Filters out fields that should not be sent (based on field config)
   * 2. Sends the filtered data to the API
   * 3. Handles success/failure and displays appropriate messages
   * 4. Refreshes the data on success
   *
   * @param key The unique identifier of the item to update
   * @param updatedData The new data to apply to the item
   */
  updateData(String key, Map<String, dynamic> updatedData) async {
    try {
      // First trim all string values
      final trimmedData = trimStringValues(updatedData);

      // Filter the data according to field configurations
      final filteredData = Map<String, dynamic>.from(trimmedData)
        ..removeWhere((key, _) {
          try {
            // Try to find the field config for this key
            final fieldConfig = fieldConfigs.firstWhere((f) => f.key == key);

            if (fieldConfig.forceSendOnUpdate == true) {
              return false; // Don't remove this field
            }
            // Remove if any of these conditions are true
            return fieldConfig.hideOnUpdate || fieldConfig.skipOnUpdate || fieldConfig.readOnly;
          } catch (e) {
            // If field config not found, log it and keep the field (return false)
            // or remove it (return true) depending on your requirements
            // print('Warning: No field configuration found for key: $key');
            // Option 1: Remove fields without configuration (safer approach)
            return true;
          }
        });

      final response = await apiClass.put(endpointUpdate(key), filteredData);
      if (debug) logger.d("Endpoint : ${endpointUpdate(key)}");
      if (debug) logger.d("Payload : $filteredData");
      logger.d("Payload : $filteredData");

      if (response.isOk) {
        if (debug) logger.d(response.body);
        toast(text: "Successfully saved", type: ToastificationType.success);
        Get.back(result: true);

        // Reload data from API
        goToFirstPage();

        // return true;
      } else {
        logger.e('Failed to load: ${response.body}');

        // Check if the response is 401 Unauthorized
        if (response.statusCode == 401) {
          _handleUnauthorized();
          return;
        }

        if (showErrorOnToast) {
          showError(response);
        } else {
          toast(text: "Failed to load", type: ToastificationType.error);
        }
        // return false;
      }
    } catch (e) {
      logger.e('Error saving data: $e');
      toast(text: "Failed to save data", type: ToastificationType.error);
      // return false;
    }
  }

  void showError(response) {
    Get.dialog(
      AlertDialog(
        title: const Text('Error'),
        content: SelectableText(response.body[showErrorOnToastFromKey] ?? "Failed to load"),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  // Handle adding new item
  void clickAddNew() {
    if (enableEdit) {
      Get.dialog(
        ItemEditPage(
          parentController: this,
        ),
        barrierDismissible: false,
      );
    }
  }

  // Create empty item with default values
  Map<String, dynamic> createEmptyItem() {
    final Map<String, dynamic> emptyItem = {};

    // Set default values for each field
    for (var field in fieldConfigs) {
      emptyItem[field.key] = field.defaultValue;
    }

    return emptyItem;
  }

  // Helper method to trim string values in a map
  Map<String, dynamic> trimStringValues(Map<String, dynamic> data) {
    final Map<String, dynamic> trimmedData = {};

    data.forEach((key, value) {
      if (value is String) {
        // Trim string values
        trimmedData[key] = value.trim();
      } else {
        // Keep non-string values as is
        trimmedData[key] = value;
      }
    });

    return trimmedData;
  }

  // Helper method to handle unauthorized responses (401)
  void _handleUnauthorized() {
    try {
      // Log the unauthorized access attempt
      logger.w('Unauthorized access detected. Logging out user.');

      // Show toast message to user
      toast(text: "Session expired. Please login again.", type: ToastificationType.warning);

      // Get HomeController and call clickLogout
      if (Get.isRegistered<HomeController>()) {
        Get.find<HomeController>().clickLogout();
      } else {
        // Fallback if HomeController is not registered
        storage.remove(keyAccessToken);
        storage.remove(keyRefreshToken);
        storage.remove(keyIsLoggedIn);
        Get.offAllNamed(Routes.LOGIN);
      }
    } catch (e) {
      // If anything goes wrong, ensure we at least redirect to login
      logger.e('Error during logout: $e');
      Get.offAllNamed(Routes.LOGIN);
    }
  }

  // Handle creating new item
  Future<bool> addData(Map<String, dynamic> data) async {
    try {
      // First trim all string values
      final trimmedData = trimStringValues(data);

      final filteredData = Map<String, dynamic>.from(trimmedData)
        ..removeWhere((key, _) {
          try {
            // Try to find the field config for this key
            final fieldConfig = fieldConfigs.firstWhere((f) => f.key == key);

            // If forceSendOnCreate is true, keep the field regardless of other settings
            if (fieldConfig.forceSendOnCreate == true) {
              return false; // Don't remove this field
            }

            // Otherwise, apply the normal filtering rules
            return fieldConfig.hideOnCreate || fieldConfig.skipOnCreate || fieldConfig.readOnly;
          } catch (e) {
            // If field config not found, remove it
            return true;
          }
        });

      // Implement actual saving logic in subclasses

      final response = await apiClass.post(endpointCreate(), filteredData);
      if (debug) logger.d("Endpoint : ${endpointCreate()}");
      if (debug) logger.d("Payload : $filteredData");

      if (response.isOk) {
        if (debug) logger.d(response.body);
        toast(text: "Successfully saved", type: ToastificationType.success);
        Get.back(result: true);

        // Reload data from API
        goToFirstPage();

        return true;
      } else {
        logger.e('Failed to load: ${response.body}');

        // Check if the response is 401 Unauthorized
        if (response.statusCode == 401) {
          _handleUnauthorized();
          return false;
        }

        if (showErrorOnToast) {
          showError(response);
        } else {
          toast(text: "Failed to load", type: ToastificationType.error);
        }

        return false;
      }
    } catch (e) {
      logger.e('Error creating new item: $e');
      return false;
    }
  }

  // Tab controller
  initTabs() {
    tabController = TabController(
      initialIndex: tabInitialIndex,
      length: tabs.length,
      vsync: this,
    );

    tabController.addListener(() {
      currentTabIndex.value = tabController.index;
    });
  }

  // Pindah ke tab tertentu
  void changeTab(int index) {
    tabController.animateTo(index);
    currentTabIndex.value = index;
  }

  @override
  void onClose() {
    // Pastikan untuk dispose TabController ketika controller dihapus
    if (tabs.isNotEmpty) {
      tabController.dispose();
    }
    super.onClose();
  }

  // Validate form input
  Map<String, String?> validateForm(Map<String, dynamic> data) {
    final Map<String, String?> errors = {};

    for (var field in fieldConfigs) {
      final value = data[field.key];

      // Check required fields
      if (field.required && (value == null || value.toString().isEmpty)) {
        errors[field.key] = 'This field is required';
        continue;
      }

      // Skip further validation if value is empty and not required
      if (value == null || value.toString().isEmpty) {
        continue;
      }

      // Field type specific validations with enhanced security
      switch (field.type) {
        case FieldType.email:
          final sanitizedEmail = InputSecurity.validateEmail(value.toString());
          if (sanitizedEmail == null) {
            errors[field.key] = 'Please enter a valid email';
          }
          break;
        case FieldType.phone:
          final sanitizedPhone = InputSecurity.validatePhone(value.toString());
          if (sanitizedPhone == null) {
            errors[field.key] = 'Please enter a valid phone number';
          }
          break;
        case FieldType.number:
          final sanitizedNumber = InputSecurity.validateNumber(value.toString());
          if (sanitizedNumber == null) {
            errors[field.key] = 'Please enter a valid number';
          }
          break;
        case FieldType.url:
          final sanitizedUrl = InputSecurity.validateUrl(value.toString());
          if (sanitizedUrl == null) {
            errors[field.key] = 'Please enter a valid URL';
          }
          break;
        case FieldType.text:
        case FieldType.multiline:
          // Check for suspicious patterns in text input
          if (InputSecurity.containsSuspiciousPatterns(value.toString())) {
            errors[field.key] = 'Input contains invalid characters';
          }
          break;
        default:
          // For other types, check for suspicious patterns
          if (InputSecurity.containsSuspiciousPatterns(value.toString())) {
            errors[field.key] = 'Input contains invalid characters';
          }
          break;
      }

      // Custom validation if provided
      if (field.validator != null) {
        final customError = field.validator!(value);
        if (customError != null) {
          errors[field.key] = customError;
        }
      }
    }

    return errors;
  }

  /// Sanitizes form data before submission
  Map<String, dynamic> sanitizeFormData(Map<String, dynamic> data) {
    final sanitizedData = <String, dynamic>{};

    for (var field in fieldConfigs) {
      final value = data[field.key];
      if (value == null) continue;

      switch (field.type) {
        case FieldType.email:
          final sanitizedEmail = InputSecurity.validateEmail(value.toString());
          sanitizedData[field.key] = sanitizedEmail ?? value;
          break;
        case FieldType.phone:
          final sanitizedPhone = InputSecurity.validatePhone(value.toString());
          sanitizedData[field.key] = sanitizedPhone ?? value;
          break;
        case FieldType.number:
          final sanitizedNumber = InputSecurity.validateNumber(value.toString());
          sanitizedData[field.key] = sanitizedNumber ?? value;
          break;
        case FieldType.url:
          final sanitizedUrl = InputSecurity.validateUrl(value.toString());
          sanitizedData[field.key] = sanitizedUrl ?? value;
          break;
        case FieldType.text:
        case FieldType.multiline:
          final sanitizedText = InputSecurity.sanitizeText(value.toString(),
              type: field.type == FieldType.multiline ? 'description' : 'general');
          sanitizedData[field.key] = sanitizedText;
          break;
        default:
          // For other types, apply basic sanitization
          if (value is String) {
            sanitizedData[field.key] = InputSecurity.sanitizeText(value);
          } else {
            sanitizedData[field.key] = value;
          }
          break;
      }
    }

    // Include any fields not in fieldConfigs (like IDs, etc.)
    for (var entry in data.entries) {
      if (!sanitizedData.containsKey(entry.key)) {
        sanitizedData[entry.key] = entry.value;
      }
    }

    return sanitizedData;
  }

  void toggleSelectionMode() {
    isSelectionMode.value = !isSelectionMode.value;
  }

  // Select/deselect methods
  void toggleItemSelection(String key) {
    if (selectedItems.contains(key)) {
      selectedItems.remove(key);
    } else {
      selectedItems.add(key);
    }
  }

  void selectAll() {
    if (items == null || items!.isEmpty) {
      return;
    }
    selectedItems.addAll(items!.keys);
  }

  void clearSelection() {
    selectedItems.clear();
  }

  /**
   * Clears all active filters and reloads the data.
   *
   * This method resets the selectedFilters map to the default filters
   * and then reloads the data to reflect the changes.
   */
  void clearAllFilters() {
    selectedFilters.clear();
    // Reset to default filters if any
    if (defaultFilters.isNotEmpty) {
      selectedFilters.addAll(defaultFilters);
    }
    // Reset to first page when filters are cleared
    currentPage.value = 0;
    // Reload data with cleared filters
    loadData();
  }

  // Delete methods
  Future<bool> deleteData(String key) async {
    try {
      // Implement actual saving logic in subclasses

      final response = await apiClass.delete(endpointDelete(key));
      if (debug) logger.d("Endpoint : ${endpointDelete(key)}");

      if (response.isOk) {
        if (debug) logger.d(response.body);
        toast(text: "Successfully delete", type: ToastificationType.success);
        Get.back(result: true);

        // Update local data
        // Reload data from API
        goToFirstPage();

        return true;
      } else {
        logger.e('Failed to load: ${response.body}');

        // Check if the response is 401 Unauthorized
        if (response.statusCode == 401) {
          _handleUnauthorized();
          return false;
        }

        if (showErrorOnToast) {
          showError(response);
        } else {
          toast(text: "Failed to load", type: ToastificationType.error);
        }

        return false;
      }
    } catch (e) {
      logger.e('Error deleting data: $e');
      return false;
    }
  }

  Future<bool> deleteSelectedItems() async {
    try {
      bool allSuccess = true;

      for (var key in selectedItems) {
        bool success = await deleteData(key);
        if (!success) {
          allSuccess = false;
        }
      }

      // Clear selection after delete
      clearSelection();

      return allSuccess;
    } catch (e) {
      logger.e('Error deleting selected items: $e');
      return false;
    }
  }
}
