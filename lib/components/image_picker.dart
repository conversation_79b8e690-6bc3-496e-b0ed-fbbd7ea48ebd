// Image picker component with compression support
import 'package:cached_network_image/cached_network_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:superapps_cms/utils/base_controller.dart';
import 'package:superapps_cms/utils/extension.dart';
import 'package:superapps_cms/utils/field_config.dart';
import 'package:superapps_cms/utils/input_security.dart';
import 'package:superapps_cms/utils/utils.dart';
import 'package:toastification/toastification.dart';

class ImagePicker extends StatelessWidget {
  ImagePicker({
    super.key,
    required String uploadUrl,
    required Function(String) onImageSelected,
    required this.imageRatio,
    String? currentImage,
    this.width = double.infinity,
    this.maxFileSize,
  }) : controller = Get.put(
          ImagePickerController(
            onImageSelected: onImageSelected,
            uploadUrl: uploadUrl,
            currentImageUrl: currentImage,
            maxFileSize: maxFileSize ?? FieldConfig.defaultMaxFileSize,
          ),
          tag: getRandomString(),
        );
  ImagePickerController controller;
  double width;
  double imageRatio;
  final int? maxFileSize;

  // Build a placeholder widget for when there's no image
  Widget _buildPlaceholder() {
    return Container(
      height: width / imageRatio,
      width: width,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () => controller.clickImage(),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_photo_alternate_outlined,
                size: 48,
                color: Colors.blue.shade300,
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.blue.shade100),
                ),
                child: const Text(
                  'Click to add image',
                ).small(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Obx(() {
          String img = controller.imagePath.value != '' ? controller.imagePath.value : controller.currentImageUrl ?? "";

          // Check if image URL is empty
          if (img.isEmpty) {
            return _buildPlaceholder();
          }

          // Add timestamp to prevent caching
          String imageWithTimestamp = img.contains('?')
              ? '$img&t=${DateTime.now().millisecondsSinceEpoch}'
              : '$img?t=${DateTime.now().millisecondsSinceEpoch}';

          return GestureDetector(
            onTap: () => controller.clickImage(),
            child: CachedNetworkImage(
              // Use a key that changes when the image changes to force rebuild
              key: ValueKey(imageWithTimestamp),
              imageUrl: imageWithTimestamp,
              height: width / imageRatio,
              width: width,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                height: width / imageRatio,
                width: width,
                color: Colors.grey.shade200,
                child: const Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
              errorWidget: (context, url, error) => _buildPlaceholder(),
            ),
          );
        }),
        // Add info icon with tooltip
        Positioned(
          top: 5,
          right: 5,
          child: Tooltip(
            message:
                'Max file size: ${FieldConfig.formatFileSize(controller.maxFileSize)}\nAllowed formats: jpg, jpeg, png\nImages will be automatically compressed to reduce file size',
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade400,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(40),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.info_outline, size: 16, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }
}

class ImagePickerController extends BaseController {
  ImagePickerController({
    required this.onImageSelected,
    required this.uploadUrl,
    this.currentImageUrl,
    required this.maxFileSize,
  }) {
    imagePath.value = currentImageUrl ?? '';
  }

  final RxString imagePath = ''.obs;
  final String uploadUrl;
  final String? currentImageUrl;
  final int maxFileSize;
  void Function(String) onImageSelected;

  Future<void> clickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowMultiple: false,
        allowedExtensions: ['png', 'jpg', 'jpeg'],
      );

      if (result != null && result.files.isNotEmpty) {
        PlatformFile file = result.files.first;

        // Enhanced file validation using security utilities
        final validationResult = InputSecurity.validateFile(
          filename: file.name,
          fileSize: file.size,
          allowedExtensions: ['png', 'jpg', 'jpeg'],
          maxFileSize: maxFileSize,
          fileBytes: file.bytes,
        );

        if (!validationResult['isValid']) {
          toast(
            text: validationResult['error'] ?? 'File validation failed',
            type: ToastificationType.error,
          );
          return;
        }

        _uploadImage(file);
      }
    } catch (e) {
      toast(text: 'Failed to pick images', type: ToastificationType.error);
    }
  }

  Future<void> _uploadImage(PlatformFile file) async {
    isUploading.value = true;

    try {
      // Try to compress the image before uploading
      Uint8List bytesToUpload;
      try {
        final Uint8List? compressedBytes = await _compressImage(file);

        if (compressedBytes == null) {
          logger.w("Compression returned null, using original bytes");
          bytesToUpload = file.bytes!;
        } else {
          bytesToUpload = compressedBytes;

          // Only show compression info if we actually compressed the image
          if (bytesToUpload.length != file.bytes!.length) {
            // Log compression results
            final compressionRatio = file.bytes!.length / bytesToUpload.length;
            final originalSizeKB = (file.bytes!.length / 1024).toStringAsFixed(2);
            final compressedSizeKB = (bytesToUpload.length / 1024).toStringAsFixed(2);

            logger.d(
                'Image compressed: ${originalSizeKB}KB → ${compressedSizeKB}KB (${compressionRatio.toStringAsFixed(2)}x)');

            // Show toast if compression achieved at least 20% reduction
            if (compressionRatio > 1.2) {
              toast(
                text: 'Image compressed: ${originalSizeKB}KB → ${compressedSizeKB}KB',
                type: ToastificationType.success,
              );
            }
          } else {
            logger.d('Image not compressed (same size or compression skipped)');
          }
        }
      } catch (e) {
        logger.e("Error during compression preparation: $e");
        // Fallback to original bytes
        bytesToUpload = file.bytes!;
      }

      final form = FormData(
        {
          'file': MultipartFile(
            bytesToUpload, // Pass processed bytes (compressed or original)
            filename: file.name,
          ),
        },
      );

      final response = await apiAgent.updateProfilePicture(form);
      if (response.isOk) {
        logger.d(response.body);
        // First update the image path
        imagePath.value = response.body['initialPreview'].first;
        // Then call the callback with the new image path
        onImageSelected(imagePath.value);
        // Force UI update
        update();
      } else {
        logger.e("Error : ${response.body}");
        toast(text: "Error uploading image", type: ToastificationType.error);
      }
    } catch (e) {
      logger.e("Error compressing/uploading image: $e");
      toast(text: "Error processing image", type: ToastificationType.error);
    } finally {
      isUploading.value = false;
    }
  }

  /// Compresses an image using optimal settings
  /// Returns compressed bytes or null if compression failed
  Future<Uint8List?> _compressImage(PlatformFile file) async {
    try {
      if (file.bytes == null) return null;

      // Always try to compress images to reduce size
      final int originalSize = file.bytes!.length;

      // Get file extension to determine format
      final String ext = file.extension?.toLowerCase() ?? '';

      // Use much more aggressive compression for all images
      int targetQuality;
      int minWidth = 800; // Default target width for resizing

      // For all images, we want to reduce size significantly
      if (originalSize > 4 * 1024 * 1024) {
        // Very large image (>4MB): extremely aggressive compression
        targetQuality = 40;
        minWidth = 1200;
      } else if (originalSize > 2 * 1024 * 1024) {
        // Large image (2-4MB): very aggressive compression
        targetQuality = 45;
        minWidth = 1400;
      } else if (originalSize > 1 * 1024 * 1024) {
        // Medium-large image (1-2MB): aggressive compression
        targetQuality = 50;
        minWidth = 1600;
      } else if (originalSize > 500 * 1024) {
        // Medium image (500KB-1MB): medium-high compression
        targetQuality = 55;
        minWidth = 1200;
      } else if (originalSize > 200 * 1024) {
        // Small-medium image (200KB-500KB): medium compression
        targetQuality = 60;
        minWidth = 1000;
      } else {
        // Small image (<200KB): medium-light compression
        targetQuality = 65;
        minWidth = 800;
      }

      // Determine format to use for compression
      CompressFormat format;
      if (ext == 'png') {
        format = CompressFormat.png;
      } else if (ext == 'heic' || ext == 'heif') {
        format = CompressFormat.heic;
      } else if (ext == 'webp') {
        format = CompressFormat.webp;
      } else {
        // Default to jpeg for best compression
        format = CompressFormat.jpeg;
      }

      // Compress the image with aggressive settings
      final result = await FlutterImageCompress.compressWithList(
        file.bytes!,
        quality: targetQuality,
        format: format,
        // Use minWidth/minHeight for resizing
        minWidth: minWidth,
        minHeight: minWidth, // Use same value for aspect ratio preservation
      );

      return result;
    } catch (e) {
      // Handle specific errors
      if (e is UnimplementedError) {
        logger.w("Image compression not implemented on this platform: $e");
      } else {
        logger.e("Error compressing image: $e");
      }

      // Return original bytes if compression fails
      return file.bytes;
    }
  }
}
