import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:superapps_cms/common_components/loader.dart';
import 'package:superapps_cms/modules/information/information_model.dart';
import 'package:superapps_cms/utils/base_controller.dart';
import 'package:superapps_cms/utils/extension.dart';
import 'package:superapps_cms/utils/field_config.dart';
import 'package:superapps_cms/utils/input_security.dart';
import 'package:superapps_cms/utils/utils.dart';
import 'package:toastification/toastification.dart';
import 'package:url_launcher/url_launcher.dart';

class MultiAttachmentWidget extends StatelessWidget {
  final String label;
  final List initialAttachments;
  final String? parentKey;
  final Function(List)? onAttachmentsChanged;
  final Function(FilePickerResult, AttachmentModel)? onDeleteAttachment;
  final bool readOnly;
  final List<AllowedFileType>? allowedFileTypes;
  final int? maxFileSize;
  final String? apiEndpoint;
  final GetConnect? apiClass;

  late final MultiAttachmentController controller;

  MultiAttachmentWidget({
    super.key,
    required this.label,
    this.parentKey,
    this.initialAttachments = const [],
    this.onAttachmentsChanged,
    this.onDeleteAttachment,
    this.readOnly = false,
    this.allowedFileTypes,
    this.maxFileSize,
    this.apiEndpoint,
    this.apiClass,
  }) : controller = Get.put(
          MultiAttachmentController(
            initialAttachments: initialAttachments,
            onAttachmentsChanged: onAttachmentsChanged,
            onUploadAttachment: onDeleteAttachment,
            parentKey: parentKey,
            allowedFileTypes: allowedFileTypes,
            maxFileSize: maxFileSize,
            apiEndpoint: apiEndpoint,
            apiClass: apiClass,
          ),
          tag: UniqueKey().toString(),
        );

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Obx(() {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Display existing attachments
              if (controller.attachmentsList.isNotEmpty)
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: controller.attachmentsList.length,
                  itemBuilder: (context, index) {
                    final attachment = controller.attachmentsList[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildAttachmentListTile(attachment, index),
                          _buildAttachmentPreview(attachment),
                        ],
                      ),
                    );
                  },
                ),

              if (controller.isLoading.isTrue) const Center(child: Loader()),
              // Add new attachment button
              if (!readOnly)
                Row(
                  children: [
                    TextButton(
                      onPressed: controller.pickAndUploadAttachment,
                      child: controller.isLoading.isTrue ? const LoaderButton() : const Text('Add Attachment'),
                    ),
                    Tooltip(
                      message: _buildTooltipMessage(),
                      child: const Icon(Icons.info_outline, size: 16, color: Colors.grey),
                    ),
                  ],
                ),
            ],
          );
        }),
      ],
    );
  }

  // Helper method to get an icon based on attachment type
  Widget _getAttachmentIcon(String attachmentType) {
    switch (attachmentType.toUpperCase()) {
      case 'IMAGE':
        return const Icon(Icons.image, color: Colors.blue);
      case 'PDF':
        return const Icon(Icons.file_present_rounded, color: Colors.blue);
      case 'DOCUMENT':
        return const Icon(Icons.description, color: Colors.blue);
      case 'VIDEO':
        return const Icon(Icons.video_library, color: Colors.blue);
      default:
        return const Icon(Icons.attach_file, color: Colors.blue);
    }
  }

  // Build the list tile for an attachment
  Widget _buildAttachmentListTile(AttachmentModel attachment, int index) {
    return ListTile(
      leading: _getAttachmentIcon(attachment.attachmentType),
      subtitle: Text(attachment.url).small(),
      trailing: readOnly
          ? null
          : IconButton(
              icon: Icon(Icons.delete_outline, color: Colors.red.shade400),
              onPressed: () => controller.removeAttachment(index),
            ),
      onTap: () => controller.openAttachment(attachment.url),
    );
  }

  // Build the appropriate preview based on attachment type
  Widget _buildAttachmentPreview(AttachmentModel attachment) {
    final attachmentType = attachment.attachmentType.toUpperCase();

    if (attachmentType == 'IMAGE') {
      return _buildImagePreview(attachment.url);
    } else if (attachmentType == 'PDF') {
      return Container();
    } else {
      return const SizedBox.shrink(); // No preview for other types
    }
  }

  // Build tooltip message with file size and type information
  String _buildTooltipMessage() {
    final maxSize = maxFileSize ?? FieldConfig.defaultMaxFileSize;
    String message = 'Max file size: ${FieldConfig.formatFileSize(maxSize)}\n';

    if (allowedFileTypes != null && allowedFileTypes!.isNotEmpty) {
      message += 'Allowed file types: ${allowedFileTypes!.map((t) => t.label).join(', ')}\n';

      // Add details about allowed extensions
      for (var type in allowedFileTypes!) {
        message += '${type.label}: ${type.extensions.join(', ')}\n';
      }
    } else {
      message += 'Allowed file types: Images, PDF, Video\n';
      message += 'Images: jpg, jpeg, png\n';
      message += 'PDF: pdf\n';
      message += 'Video: mp4';
    }

    return message;
  }

  // Build image preview widget
  Widget _buildImagePreview(String imageUrl) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(
          imageUrl,
          height: 200,
          width: double.infinity,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              height: 200,
              width: double.infinity,
              color: Colors.grey[200],
              child: const Center(
                child: Icon(Icons.broken_image, size: 50, color: Colors.grey),
              ),
            );
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              height: 200,
              width: double.infinity,
              color: Colors.grey[200],
              child: Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                      : null,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class MultiAttachmentController extends BaseController {
  RxList<AttachmentModel> attachmentsList = RxList<AttachmentModel>();
  final Function(List<AttachmentModel>)? onAttachmentsChanged;
  final Function(FilePickerResult, AttachmentModel)? onUploadAttachment;
  final String? parentKey;
  final List<AllowedFileType>? allowedFileTypes;
  final int? maxFileSize;
  final String? apiEndpoint;
  final GetConnect? apiClass;

  MultiAttachmentController({
    List? initialAttachments,
    this.parentKey,
    this.onAttachmentsChanged,
    this.onUploadAttachment,
    this.allowedFileTypes,
    this.maxFileSize,
    this.apiEndpoint,
    this.apiClass,
  }) {
    if (initialAttachments != null) {
      // if (initialAttachments is List<Map>) {
      attachmentsList.value = initialAttachments.map((item) => AttachmentModel.fromJson(item)).toList();
      // } else {
      //   attachmentsList.value = List<AttachmentModel>.from(initialAttachments);
      // }
    }
  }

  Future<void> addAttachment(AttachmentModel attachment) async {
    attachmentsList.add(attachment);
    if (onAttachmentsChanged != null) {
      onAttachmentsChanged!(attachmentsList);
    }
  }

  void removeAttachment(int index) {
    if (index >= 0 && index < attachmentsList.length) {
      final attachment = attachmentsList[index];

      // If attachment has an ID and we have API access, show confirmation and call API
      if (attachment.id != null && attachment.id! > 0 && apiEndpoint != null && apiClass != null && parentKey != null) {
        _showDeleteConfirmation(attachment, index);
      } else {
        // For new attachments without ID, just remove locally
        attachmentsList.removeAt(index);
        if (onAttachmentsChanged != null) {
          onAttachmentsChanged!(attachmentsList);
        }
      }
    }
  }

  void _showDeleteConfirmation(AttachmentModel attachment, int index) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Attachment'),
        content: const Text('Are you sure you want to delete this attachment? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Get.back(); // Close dialog first
              await _deleteAttachmentFromAPI(attachment, index);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteAttachmentFromAPI(AttachmentModel attachment, int index) async {
    try {
      isLoading.value = true;

      // Construct the API endpoint: {apiEndpoint}/{parentKey}/attachment/{attachmentId}
      final deleteEndpoint = '$apiEndpoint/attachment/${attachment.id}';

      final response = await apiClass!.delete(deleteEndpoint);

      if (response.isOk) {
        // Remove from local list on successful API call
        attachmentsList.removeAt(index);
        if (onAttachmentsChanged != null) {
          onAttachmentsChanged!(attachmentsList);
        }
        toast(text: "Attachment deleted successfully", type: ToastificationType.success);
      } else {
        logger.e('Failed to delete attachment: ${response.body}');
        toast(text: "Failed to delete attachment", type: ToastificationType.error);
      }
    } catch (e) {
      logger.e('Error deleting attachment: $e');
      toast(text: "Error deleting attachment", type: ToastificationType.error);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> pickAndUploadAttachment() async {
    try {
      // Determine allowed file types
      FileType fileType = FileType.any;
      List<String>? allowedExtensions;

      if (allowedFileTypes != null && allowedFileTypes!.isNotEmpty) {
        // If specific file types are allowed, collect their extensions
        fileType = FileType.custom;
        allowedExtensions = [];

        for (var type in allowedFileTypes!) {
          allowedExtensions.addAll(type.extensions);
        }
      }

      final result = await FilePicker.platform.pickFiles(
        type: fileType,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // Enhanced file validation using security utilities
        List<String> allowedExts = [];
        if (allowedFileTypes != null && allowedFileTypes!.isNotEmpty) {
          for (var type in allowedFileTypes!) {
            allowedExts.addAll(type.extensions);
          }
        } else {
          // Default allowed extensions
          allowedExts = ['jpg', 'jpeg', 'png', 'pdf', 'mp4'];
        }

        final validationResult = InputSecurity.validateFile(
          filename: file.name,
          fileSize: file.size,
          allowedExtensions: allowedExts,
          maxFileSize: maxFileSize ?? FieldConfig.defaultMaxFileSize,
          fileBytes: file.bytes,
        );

        if (!validationResult['isValid']) {
          toast(
            text: validationResult['error'] ?? 'File validation failed',
            type: ToastificationType.error,
          );
          return;
        }

        // Show loading indicator
        isLoading.value = true;

        // Create a new attachment
        final newAttachment = AttachmentModel(
          id: 0, // Will be assigned by the server
          url: file.path ?? '',
          attachmentType: _getAttachmentTypeFromFile(file.extension),
        );

        // Upload the file to the server
        final uploadedUrl = await _uploadFileToServer(file);

        if (uploadedUrl != null) {
          // Update the attachment with the server URL
          newAttachment.url = uploadedUrl;

          // Add to the list - this should trigger UI update
          addAttachment(newAttachment);

          // Force refresh if needed
          update();
        }

        isLoading.value = false;
      }
    } catch (e) {
      logger.e('Error picking and uploading attachment: $e');
      isLoading.value = false;
    }
  }

  // New method to upload file to server
  Future<String?> _uploadFileToServer(PlatformFile file) async {
    try {
      if (file.path == null) return null;

      // Create form data for the request
      final formData = FormData({
        'file': MultipartFile(
          file.bytes ?? File(file.path!).readAsBytesSync(),
          filename: file.name,
        ),
      });

      // Send the request to the information upload endpoint
      final response = await apiCommon.post(
        apiCommon.informationUpload,
        formData,
      );

      // Check if successful
      if (response.isOk) {
        final responseData = response.body;

        // Extract URL from the specific response structure
        if (responseData != null &&
            responseData['initialPreviewConfig'] != null &&
            responseData['initialPreviewConfig'].isNotEmpty) {
          // Get the URL from the extra.url field in the first config item
          final url = responseData['initialPreviewConfig'][0]['extra']['url'];

          if (url != null && url.isNotEmpty) {
            return url;
          }

          // Fallback to initialPreview if extra.url is not available
          if (responseData['initialPreview'] != null && responseData['initialPreview'].isNotEmpty) {
            return responseData['initialPreview'][0];
          }
        }
      } else {
        logger.e(response.body);
      }

      return null;
    } catch (e) {
      logger.e('Error uploading file: $e');
      return null;
    }
  }

  void openAttachment(String url) {
    if (url.isNotEmpty) {
      launchUrl(Uri.parse(url));
    }
  }

  // Helper method to determine attachment type from file extension
  String _getAttachmentTypeFromFile(String? extension) {
    if (extension == null) return 'DOCUMENT';

    switch (extension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
        return 'IMAGE';
      case 'pdf':
        return 'DOCUMENT';
      case 'doc':
      case 'docx':
      case 'txt':
      case 'rtf':
        return 'DOCUMENT';
      case 'mp4':
      case 'mov':
      case 'avi':
      case 'webm':
        return 'VIDEO';
      default:
        return 'DOCUMENT';
    }
  }
}
