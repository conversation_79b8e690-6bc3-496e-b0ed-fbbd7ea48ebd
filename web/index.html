<!DOCTYPE html>
<html>
  <head>
    <base href="$FLUTTER_BASE_HREF" />
    <meta charset="UTF-8" />
    <meta content="IE=Edge" http-equiv="X-UA-Compatible" />
    <meta name="description" content="Secure CMS Application" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />

    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    <meta name="referrer" content="strict-origin-when-cross-origin" />

    <!-- Content Security Policy -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://www.gstatic.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://nrwvafwa4f.execute-api.ap-southeast-3.amazonaws.com https://sa-common-pdl-uat.qloudkita.id https://www.gstatic.com https://fonts.gstatic.com wss:; media-src 'self' data: blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests" />
    <!-- iOS meta tags & icons -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-title" content="superapps_cms" />
    <link rel="apple-touch-icon" href="icons/Icon-192.png" />
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <title>superapps_cms</title>
    <link rel="manifest" href="manifest.json" />
    <script>
      // The value below is injected by flutter build, do not touch.
      const serviceWorkerVersion = null;
    </script>
    <!-- Flutter initialization script -->
    <script src="flutter.js" defer></script>
    <!-- Load Firebase config script -->
    <script src="firebase-config.js"></script>
    <!-- Load App config script -->
    <script src="app-config.js"></script>
    <!-- Load Security Headers script -->
    <script src="security_headers.js"></script>
    <script src="https://unpkg.com/pica/dist/pica.min.js" ></script>

    <style>
      /* Fallback fonts in case Google Fonts fails to load */
      @font-face {
        font-family: 'Roboto-Fallback';
        src: local('Roboto'), local('Arial'), local('Helvetica'), local('sans-serif');
        font-display: swap;
      }

      body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        background: linear-gradient(to bottom right, #E1E8F8, #F8E1E8);
        font-family: 'Roboto', 'Roboto-Fallback', Arial, Helvetica, sans-serif;
      }
      
      .loading-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100vw;
      }
      
      .loading-image {
        width: 200px;
        height: auto;
        margin-bottom: 24px;
      }
      
      .loading-indicator {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-top: 3px solid #2196F3;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .app-loading-text {
        margin-top: 16px;
        font-family: Arial, sans-serif;
        font-size: 14px;
        color: #666666;
      }
    </style>
  </head>
  <body>
    <div id="loading-container" class="loading-container">
      <img src="launch_icon.png" class="loading-image" alt="App Logo">
      <div class="loading-indicator"></div>
    </div>
    
    <script>
      window.addEventListener("load", function (ev) {
        // Download main.dart.js
        _flutter.loader.loadEntrypoint({
          serviceWorker: {
            serviceWorkerVersion: serviceWorkerVersion,
          },
          onEntrypointLoaded: function (engineInitializer) {
            engineInitializer.initializeEngine().then(function (appRunner) {
              // Hide loading screen when Flutter app is ready
              document.getElementById('loading-container').style.display = 'none';
              appRunner.runApp();
            });
          },
        });
      });
    </script>
    <script type="module">
      // Firebase initialization with fallback CDN support
      const FIREBASE_VERSION = "10.12.2"; // Using stable version

      // Function to try importing from different CDN sources
      const tryImportFirebase = async () => {
        const cdnSources = [
          `https://www.gstatic.com/firebasejs/${FIREBASE_VERSION}`,
          `https://cdn.jsdelivr.net/npm/firebase@${FIREBASE_VERSION}/+esm`,
          `https://unpkg.com/firebase@${FIREBASE_VERSION}/+esm`
        ];

        for (const cdnBase of cdnSources) {
          try {
            console.log(`Trying Firebase CDN: ${cdnBase}`);
            const { initializeApp } = await import(`${cdnBase}/firebase-app.js`);
            const { getStorage } = await import(`${cdnBase}/firebase-storage.js`);
            const { getAuth } = await import(`${cdnBase}/firebase-auth.js`);
            const { getFirestore } = await import(`${cdnBase}/firebase-firestore.js`);

            console.log(`Successfully loaded Firebase from: ${cdnBase}`);
            return { initializeApp, getStorage, getAuth, getFirestore };
          } catch (error) {
            console.warn(`Failed to load Firebase from ${cdnBase}:`, error);
            continue;
          }
        }
        throw new Error("All Firebase CDN sources failed");
      };

      try {
        const { initializeApp, getStorage, getAuth, getFirestore } = await tryImportFirebase();

        // Initialize Firebase with dynamic config
        const initFirebase = async () => {
          try {
            // Load config from the function in firebase-config.js
            const firebaseConfig = await loadFirebaseConfig();

            if (!firebaseConfig) {
              console.error("Failed to load Firebase configuration");
              return;
            }

            // Initialize Firebase
            const app = initializeApp(firebaseConfig);

            // Initialize services and expose to window object so Flutter can access them
            const storage = getStorage(app);
            const auth = getAuth(app);
            const firestore = getFirestore(app);

            // Expose to window object for Flutter plugins
            window.firebase = {
              app: () => app,
              auth: () => auth,
              firestore: () => firestore,
              storage: () => storage,
            };

            console.log("Firebase initialized with modular imports");
          } catch (error) {
            console.error("Error initializing Firebase:", error);
            // Fallback: Set empty firebase object to prevent Flutter errors
            window.firebase = {
              app: () => null,
              auth: () => null,
              firestore: () => null,
              storage: () => null,
            };
          }
        };

        // Run the initialization
        await initFirebase();

      } catch (importError) {
        console.error("Error importing Firebase modules:", importError);
        // Fallback: Set empty firebase object to prevent Flutter errors
        window.firebase = {
          app: () => null,
          auth: () => null,
          firestore: () => null,
          storage: () => null,
        };
      }
    </script>
  </body>
</html>
