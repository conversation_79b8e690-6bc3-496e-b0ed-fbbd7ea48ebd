/**
 * Security Headers Configuration for Flutter Web
 * 
 * This script adds additional security headers and configurations
 * for the Flutter web application to enhance security.
 */

// Content Security Policy configuration
const CSP_POLICY = [
  "default-src 'self'",
  "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://www.gstatic.com", // Flutter requires unsafe-inline and unsafe-eval, allow Firebase
  "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
  "font-src 'self' https://fonts.gstatic.com",
  "img-src 'self' data: https: blob:",
  "connect-src 'self' https://nrwvafwa4f.execute-api.ap-southeast-3.amazonaws.com https://sa-common-pdl-uat.qloudkita.id https://www.gstatic.com https://fonts.gstatic.com wss:",
  "media-src 'self' data: blob:",
  "object-src 'none'",
  "base-uri 'self'",
  "form-action 'self'",
  "frame-ancestors 'none'",
  "upgrade-insecure-requests"
].join('; ');

// Security headers to add
const SECURITY_HEADERS = {
  'Content-Security-Policy': CSP_POLICY,
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
};

// Function to add security headers (if running in a service worker context)
function addSecurityHeaders() {
  if (typeof self !== 'undefined' && self.addEventListener) {
    self.addEventListener('fetch', function(event) {
      if (event.request.destination === 'document') {
        event.respondWith(
          fetch(event.request).then(function(response) {
            const newHeaders = new Headers(response.headers);
            
            // Add security headers
            Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
              newHeaders.set(key, value);
            });
            
            return new Response(response.body, {
              status: response.status,
              statusText: response.statusText,
              headers: newHeaders
            });
          })
        );
      }
    });
  }
}

// Initialize security headers
if (typeof window !== 'undefined') {
  // Client-side security configurations
  
  // Disable right-click context menu in production
  if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
    document.addEventListener('contextmenu', function(e) {
      e.preventDefault();
      return false;
    });
    
    // Disable F12, Ctrl+Shift+I, Ctrl+U
    document.addEventListener('keydown', function(e) {
      if (e.key === 'F12' || 
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.key === 'u')) {
        e.preventDefault();
        return false;
      }
    });
  }
  
  // Add meta tags for security
  const metaTags = [
    { name: 'referrer', content: 'strict-origin-when-cross-origin' },
    { 'http-equiv': 'X-Content-Type-Options', content: 'nosniff' },
    { 'http-equiv': 'X-Frame-Options', content: 'DENY' },
    { 'http-equiv': 'X-XSS-Protection', content: '1; mode=block' }
  ];
  
  metaTags.forEach(tag => {
    const meta = document.createElement('meta');
    Object.entries(tag).forEach(([key, value]) => {
      meta.setAttribute(key, value);
    });
    document.head.appendChild(meta);
  });
  
  // Clear sensitive data on page unload
  window.addEventListener('beforeunload', function() {
    // Clear any sensitive form data
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      const inputs = form.querySelectorAll('input[type="password"], input[type="email"]');
      inputs.forEach(input => {
        input.value = '';
      });
    });
    
    // Clear localStorage of sensitive data (keep only necessary items)
    const sensitiveKeys = ['accessToken', 'refreshToken', 'userProfile'];
    sensitiveKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
      }
    });
  });
  
  // Monitor for suspicious activity
  let rapidClickCount = 0;
  let lastClickTime = 0;
  
  document.addEventListener('click', function() {
    const now = Date.now();
    if (now - lastClickTime < 100) {
      rapidClickCount++;
      if (rapidClickCount > 10) {
        console.warn('Suspicious rapid clicking detected');
        // Could implement additional security measures here
      }
    } else {
      rapidClickCount = 0;
    }
    lastClickTime = now;
  });
  
} else if (typeof self !== 'undefined') {
  // Service worker context
  addSecurityHeaders();
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    SECURITY_HEADERS,
    CSP_POLICY,
    addSecurityHeaders
  };
}
